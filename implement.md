# Organization Management API - Implementation Plan

## Overview
Implement a complete CRUD API for Organization management following the Domain-Nested REST API Structure with Dependency Injection and Functional Options pattern established in the codebase. This will create a new shared REST module and integrate it into the onramp microservice.

## Architecture Pattern
The implementation follows the **Domain-Nested** structure with **Dependency Injection** used throughout the codebase:
```
shared/rest/onramp/organization/
├── handler.go          # HTTP handlers with dependency injection
├── schemas.go          # Data models and DTOs
├── errors.go           # Error definitions
└── handler_test.go     # Test files with mocked dependencies
```

### Key Architectural Principles
1. **Dependency Injection**: All external dependencies are injected through function parameters
2. **Functional Options**: Handler functions accept dependency structs for testability
3. **Clean Separation**: Business logic, data access, and HTTP handling are cleanly separated
4. **Testability**: All dependencies can be mocked for comprehensive testing

## 1. Database Schema Implementation

<!--
### 1.1 Update PostgreSQL Schema
**File**: `/schemas/data-core-pg/DDL/organization.sql`

```sql
-- Update Organization table with new columns
ALTER TABLE Organization ADD COLUMN IF NOT EXISTS CreatedAt TIMESTAMP DEFAULT NOW();
ALTER TABLE Organization ADD COLUMN IF NOT EXISTS UpdatedAt TIMESTAMP DEFAULT NOW();
ALTER TABLE Organization ADD COLUMN IF NOT EXISTS DeletedAt TIMESTAMP NULL;
ALTER TABLE Organization ADD COLUMN IF NOT EXISTS IsDeleted BOOLEAN DEFAULT FALSE;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_organization_isdeleted ON Organization(IsDeleted);
CREATE INDEX IF NOT EXISTS idx_organization_identifier ON Organization(OrganizationIdentifier);
CREATE INDEX IF NOT EXISTS idx_organization_createdat ON Organization(CreatedAt);
```

### 1.2 Database Migration Script
**File**: `/schemas/data-core-pg/migrations/add_organization_audit_fields.sql`
-->

## 2. Shared REST Module Creation

### 2.1 Module Structure (Domain-Nested with Dependency Injection)
```
/shared/rest/
├── go.mod                          # New Go module
├── go.sum
├── README.md                       # Module documentation
├── coverage.out
└── onramp/                         # Service-specific handlers
    └── organization/               # Resource handlers
        ├── handler.go              # HTTP handlers with dependency injection
        ├── handler_test.go         # Handler tests with mocked dependencies
        ├── schemas.go              # Request/response schemas and data models
        └── errors.go               # Domain-specific errors
```

### Key Changes from Traditional Structure
- **Consolidated Architecture**: Business logic is embedded within handlers using dependency injection
- **Dependency Injection**: All external dependencies (database, services) are injected for testability
- **Functional Options**: Handler functions accept dependency structs
- **Simplified Structure**: Fewer files with cleaner separation of concerns

### 2.2 Go Module Setup
**File**: `/shared/rest/go.mod`
```go
module synapse-its.com/shared/rest

go 1.24.1

require (
    github.com/google/uuid v1.6.0
    github.com/gorilla/mux v1.8.1
    github.com/stretchr/testify v1.10.0
    synapse-its.com/shared/api v0.0.0-00010101000000-000000000000
    synapse-its.com/shared/connect v0.0.0-00010101000000-000000000000
    synapse-its.com/shared/logger v0.0.0-00010101000000-000000000000
)

replace synapse-its.com/shared/api => /shared/api
replace synapse-its.com/shared/connect => /shared/connect
replace synapse-its.com/shared/logger => /shared/logger
```

## 3. Data Models and Schemas

### 3.1 Database Model and DTOs
**File**: `/shared/rest/onramp/organization/schemas.go`

```go
package organization

import "time"

// Organization represents the database model
type Organization struct {
    ID                     int        `json:"-" db:"id"`
    OrganizationIdentifier string     `json:"organizationidentifier" db:"organizationidentifier"`
    Description            string     `json:"description" db:"description"`
    APIKey                 string     `json:"apikey" db:"apikey"`
    CreatedAt              time.Time  `json:"createdat" db:"createdat"`
    UpdatedAt              time.Time  `json:"updatedat" db:"updatedat"`
    DeletedAt              *time.Time `json:"-" db:"deletedat"`
    IsDeleted              bool       `json:"-" db:"isdeleted"`
}

// CreateOrganizationRequest represents the POST request body
type CreateOrganizationRequest struct {
    Description string `json:"description"`
}

// UpdateOrganizationRequest represents the PATCH request body
type UpdateOrganizationRequest struct {
    Description string `json:"description"`
}

// OrganizationResponse represents the API response (excludes internal fields)
type OrganizationResponse struct {
    OrganizationIdentifier string    `json:"organizationidentifier"`
    Description            string    `json:"description"`
    APIKey                 string    `json:"apikey"`
    CreatedAt              time.Time `json:"createdat"`
    UpdatedAt              time.Time `json:"updatedat"`
}

// ToResponse converts Organization to OrganizationResponse
func (o *Organization) ToResponse() OrganizationResponse {
    return OrganizationResponse{
        OrganizationIdentifier: o.OrganizationIdentifier,
        Description:            o.Description,
        APIKey:                 o.APIKey,
        CreatedAt:              o.CreatedAt,
        UpdatedAt:              o.UpdatedAt,
    }
}

// Helper methods for business logic
func (o *Organization) IsValid() bool {
    return o.OrganizationIdentifier != "" &&
           o.Description != "" &&
           o.APIKey != "" &&
           !o.CreatedAt.IsZero() &&
           !o.UpdatedAt.IsZero()
}

func (o *Organization) MarkAsDeleted() {
    o.IsDeleted = true
    now := time.Now()
    o.DeletedAt = &now
    o.UpdatedAt = now
}

func (o *Organization) UpdateDescription(description string) {
    o.Description = description
    o.UpdatedAt = time.Now()
}
```

### 3.2 Error Definitions
**File**: `/shared/rest/onramp/organization/errors.go`

```go
package organization

import "errors"

var (
    ErrOrganizationNotFound      = errors.New("organization not found")
    ErrInvalidDescription        = errors.New("description cannot be empty")
    ErrUnexpectedFields          = errors.New("request contains unexpected fields")
    ErrInvalidIdentifier         = errors.New("invalid organization identifier")
    ErrOrganizationAlreadyExists = errors.New("organization already exists")
    ErrDatabaseOperation         = errors.New("database operation failed")
    ErrInvalidRequestBody        = errors.New("invalid request body")
    ErrMissingRequiredField      = errors.New("missing required field")
    ErrInvalidFieldValue         = errors.New("invalid field value")
    ErrConcurrentModification    = errors.New("concurrent modification detected")
    ErrInvalidUUID               = errors.New("invalid UUID format")
    ErrPermissionDenied          = errors.New("permission denied")
    ErrOperationNotAllowed       = errors.New("operation not allowed")
)

// Error classification helpers
func IsNotFoundError(err error) bool {
    return errors.Is(err, ErrOrganizationNotFound)
}

func IsValidationError(err error) bool {
    return errors.Is(err, ErrInvalidDescription) ||
           errors.Is(err, ErrUnexpectedFields) ||
           errors.Is(err, ErrInvalidIdentifier) ||
           errors.Is(err, ErrInvalidRequestBody) ||
           errors.Is(err, ErrMissingRequiredField) ||
           errors.Is(err, ErrInvalidFieldValue)
}

func IsDatabaseError(err error) bool {
    return errors.Is(err, ErrDatabaseOperation)
}

func IsBusinessLogicError(err error) bool {
    return errors.Is(err, ErrOrganizationAlreadyExists) ||
           errors.Is(err, ErrOperationNotAllowed) ||
           errors.Is(err, ErrConcurrentModification) ||
           errors.Is(err, ErrPermissionDenied)
}

// GetErrorMessage returns user-friendly error messages
func GetErrorMessage(err error) string {
    switch {
    case errors.Is(err, ErrOrganizationNotFound):
        return "The requested organization was not found"
    case errors.Is(err, ErrInvalidDescription):
        return "Organization description is required and cannot be empty"
    case errors.Is(err, ErrUnexpectedFields):
        return "Request contains fields that are not allowed"
    case errors.Is(err, ErrInvalidIdentifier):
        return "Organization identifier is invalid"
    case errors.Is(err, ErrInvalidRequestBody):
        return "Request body is malformed or invalid"
    case errors.Is(err, ErrDatabaseOperation):
        return "A database error occurred while processing the request"
    case errors.Is(err, ErrOrganizationAlreadyExists):
        return "An organization with this identifier already exists"
    case errors.Is(err, ErrOperationNotAllowed):
        return "This operation is not allowed"
    case errors.Is(err, ErrMissingRequiredField):
        return "One or more required fields are missing"
    case errors.Is(err, ErrInvalidFieldValue):
        return "One or more fields contain invalid values"
    case errors.Is(err, ErrConcurrentModification):
        return "The organization was modified by another process"
    case errors.Is(err, ErrInvalidUUID):
        return "Invalid UUID format provided"
    case errors.Is(err, ErrPermissionDenied):
        return "You do not have permission to perform this operation"
    default:
        return "An unexpected error occurred"
    }
}
```

## 4. HTTP Handlers with Dependency Injection

### 4.1 Handler Dependencies Structure
**File**: `/shared/rest/onramp/organization/handler.go`

```go
package organization

import (
    "context"
    "database/sql"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "strings"
    "time"

    "github.com/google/uuid"
    "github.com/gorilla/mux"
    "synapse-its.com/shared/api/response"
    "synapse-its.com/shared/connect"
    "synapse-its.com/shared/logger"
)

// HandlerDeps bundles dependencies for injection and testing
type HandlerDeps struct {
    GetConnections      func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
    CreateOrganization  func(pg connect.DatabaseExecutor, req CreateOrganizationRequest) (*Organization, error)
    GetAllOrganizations func(pg connect.DatabaseExecutor) ([]Organization, error)
    GetOrganization     func(pg connect.DatabaseExecutor, identifier string) (*Organization, error)
    UpdateOrganization  func(pg connect.DatabaseExecutor, identifier string, req UpdateOrganizationRequest) (*Organization, error)
    DeleteOrganization  func(pg connect.DatabaseExecutor, identifier string) error
}

// CreateHandlerWithDeps returns an http.HandlerFunc with injected dependencies
func CreateHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()

        // Get database connection using dependency
        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("failed to get connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        // Parse request body
        req, err := parseCreateRequest(r.Body)
        if err != nil {
            logger.Errorf("failed to create organization: %v", err)
            if IsValidationError(err) {
                response.CreateBadRequestResponse(w)
            } else {
                response.CreateBadRequestResponse(w)
            }
            return
        }

        // Create organization using dependency
        org, err := deps.CreateOrganization(connections.Postgres, req)
        if err != nil {
            logger.Errorf("failed to create organization: %v", err)
            if IsValidationError(err) {
                response.CreateBadRequestResponse(w)
            } else {
                response.CreateInternalErrorResponse(w)
            }
            return
        }

        response.CreateSuccessResponse(org.ToResponse(), w)
    }
}

// GetAllHandlerWithDeps returns an http.HandlerFunc with injected dependencies
func GetAllHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()

        // Get database connection using dependency
        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("failed to get connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        // Get all organizations using dependency
        orgs, err := deps.GetAllOrganizations(connections.Postgres)
        if err != nil {
            logger.Errorf("failed to get organizations: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        // Convert to response format
        responses := make([]OrganizationResponse, len(orgs))
        for i, org := range orgs {
            responses[i] = org.ToResponse()
        }

        response.CreateSuccessResponse(responses, w)
    }
}

// Production-ready HTTP handlers using default dependencies
var (
    CreateHandler = CreateHandlerWithDeps(HandlerDeps{
        GetConnections:     connect.GetConnections,
        CreateOrganization: createOrganization,
    })

    GetAllHandler = GetAllHandlerWithDeps(HandlerDeps{
        GetConnections:      connect.GetConnections,
        GetAllOrganizations: getAllOrganizations,
    })

    GetByIdentifierHandler = GetByIdentifierHandlerWithDeps(HandlerDeps{
        GetConnections:  connect.GetConnections,
        GetOrganization: getOrganizationByIdentifier,
    })

    UpdateHandler = UpdateHandlerWithDeps(HandlerDeps{
        GetConnections:     connect.GetConnections,
        UpdateOrganization: updateOrganization,
    })

    DeleteHandler = DeleteHandlerWithDeps(HandlerDeps{
        GetConnections:     connect.GetConnections,
        DeleteOrganization: deleteOrganization,
    })
)

// Business logic functions (injected as dependencies)
var createOrganization = func(pg connect.DatabaseExecutor, req CreateOrganizationRequest) (*Organization, error) {
    // Validate request
    if err := validateCreateRequest(req); err != nil {
        return nil, err
    }

    orgIdentifier := uuid.New().String()
    apiKey := uuid.New().String()
    now := time.Now()

    query := `
        INSERT INTO {{Organization}} (organizationidentifier, description, apikey, createdat, updatedat, isdeleted)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, organizationidentifier, description, apikey, createdat, updatedat, deletedat, isdeleted`

    var org Organization
    err := pg.QueryRowStruct(&org, query, orgIdentifier, req.Description, apiKey, now, now, false)
    if err != nil {
        logger.Errorf("failed to create organization: %v", err)
        return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    return &org, nil
}

var getAllOrganizations = func(pg connect.DatabaseExecutor) ([]Organization, error) {
    query := `
        SELECT id, organizationidentifier, description, apikey, createdat, updatedat, deletedat, isdeleted
        FROM {{Organization}}
        WHERE isdeleted = false
        ORDER BY createdat DESC`

    var orgs []Organization
    err := pg.QueryGenericSlice(&orgs, query)
    if err != nil {
        logger.Errorf("failed to get organizations: %v", err)
        return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    return orgs, nil
}

var getOrganizationByIdentifier = func(pg connect.DatabaseExecutor, identifier string) (*Organization, error) {
    // Validate identifier
    if err := validateIdentifier(identifier); err != nil {
        return nil, err
    }

    query := `
        SELECT id, organizationidentifier, description, apikey, createdat, updatedat, deletedat, isdeleted
        FROM {{Organization}}
        WHERE organizationidentifier = $1 AND isdeleted = false`

    var org Organization
    err := pg.QueryRowStruct(&org, query, identifier)
    if err != nil {
        if err == sql.ErrNoRows {
            return nil, ErrOrganizationNotFound
        }
        logger.Errorf("failed to get organization by identifier: %v", err)
        return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    return &org, nil
}

var updateOrganization = func(pg connect.DatabaseExecutor, identifier string, req UpdateOrganizationRequest) (*Organization, error) {
    // Validate inputs
    if err := validateIdentifier(identifier); err != nil {
        return nil, err
    }
    if err := validateUpdateRequest(req); err != nil {
        return nil, err
    }

    now := time.Now()

    query := `
        UPDATE {{Organization}}
        SET description = $1, updatedat = $2
        WHERE organizationidentifier = $3 AND isdeleted = false
        RETURNING id, organizationidentifier, description, apikey, createdat, updatedat, deletedat, isdeleted`

    var org Organization
    err := pg.QueryRowStruct(&org, query, req.Description, now, identifier)
    if err != nil {
        if err == sql.ErrNoRows {
            return nil, ErrOrganizationNotFound
        }
        logger.Errorf("failed to update organization: %v", err)
        return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    return &org, nil
}

var deleteOrganization = func(pg connect.DatabaseExecutor, identifier string) error {
    // Validate identifier
    if err := validateIdentifier(identifier); err != nil {
        return err
    }

    now := time.Now()

    query := `
        UPDATE {{Organization}}
        SET isdeleted = true, deletedat = $1, updatedat = $1
        WHERE organizationidentifier = $2 AND isdeleted = false`

    result, err := pg.Exec(query, now, identifier)
    if err != nil {
        logger.Errorf("failed to soft delete organization: %v", err)
        return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
    }

    if result != nil {
        rowsAffected, err := result.RowsAffected()
        if err != nil {
            logger.Errorf("failed to get rows affected: %v", err)
            return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
        }

        if rowsAffected == 0 {
            return ErrOrganizationNotFound
        }
    }

    return nil
}
// Validation functions
func validateCreateRequest(req CreateOrganizationRequest) error {
    if strings.TrimSpace(req.Description) == "" {
        return ErrInvalidDescription
    }
    return nil
}

func validateUpdateRequest(req UpdateOrganizationRequest) error {
    if strings.TrimSpace(req.Description) == "" {
        return ErrInvalidDescription
    }
    return nil
}

func validateIdentifier(identifier string) error {
    if strings.TrimSpace(identifier) == "" {
        return ErrInvalidIdentifier
    }
    return nil
}

// Request parsing utilities
func parseCreateRequest(body io.Reader) (CreateOrganizationRequest, error) {
    var req CreateOrganizationRequest

    decoder := json.NewDecoder(body)
    decoder.DisallowUnknownFields() // Reject unexpected fields

    if err := decoder.Decode(&req); err != nil {
        logger.Infof("failed to parse create request: %v", err)
        if strings.Contains(err.Error(), "unknown field") {
            return req, ErrUnexpectedFields
        }
        return req, fmt.Errorf("%w: %v", ErrInvalidRequestBody, err)
    }

    return req, nil
}

func parseUpdateRequest(body io.Reader) (UpdateOrganizationRequest, error) {
    var req UpdateOrganizationRequest

    decoder := json.NewDecoder(body)
    decoder.DisallowUnknownFields() // Reject unexpected fields

    if err := decoder.Decode(&req); err != nil {
        logger.Infof("failed to parse update request: %v", err)
        if strings.Contains(err.Error(), "unknown field") {
            return req, ErrUnexpectedFields
        }
        return req, fmt.Errorf("%w: %v", ErrInvalidRequestBody, err)
    }

    return req, nil
}

// Helper function to generate new organization with UUIDs
func GenerateNewOrganization(description string) *Organization {
    now := time.Now()
    return &Organization{
        OrganizationIdentifier: uuid.New().String(),
        Description:            description,
        APIKey:                 uuid.New().String(),
        CreatedAt:              now,
        UpdatedAt:              now,
        IsDeleted:              false,
    }
}
```

## 5. Additional Handler Functions

### 5.1 Remaining Handler Implementations

```go
// GetByIdentifierHandlerWithDeps returns an http.HandlerFunc with injected dependencies
func GetByIdentifierHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()
        vars := mux.Vars(r)
        identifier := vars["identifier"]

        // Get database connection using dependency
        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("failed to get connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        // Get organization using dependency
        org, err := deps.GetOrganization(connections.Postgres, identifier)
        if err != nil {
            if IsNotFoundError(err) {
                response.CreateNotFoundResponse(w)
            } else if IsValidationError(err) {
                response.CreateNotFoundResponse(w)
            } else {
                logger.Errorf("failed to get organization: %v", err)
                response.CreateInternalErrorResponse(w)
            }
            return
        }

        response.CreateSuccessResponse(org.ToResponse(), w)
    }
}

// UpdateHandlerWithDeps returns an http.HandlerFunc with injected dependencies
func UpdateHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()
        vars := mux.Vars(r)
        identifier := vars["identifier"]

        // Get database connection using dependency
        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("failed to get connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        // Parse request body
        req, err := parseUpdateRequest(r.Body)
        if err != nil {
            logger.Errorf("failed to parse update request: %v", err)
            if IsValidationError(err) {
                response.CreateBadRequestResponse(w)
            } else {
                response.CreateBadRequestResponse(w)
            }
            return
        }

        // Update organization using dependency
        org, err := deps.UpdateOrganization(connections.Postgres, identifier, req)
        if err != nil {
            if IsNotFoundError(err) {
                response.CreateNotFoundResponse(w)
            } else if IsValidationError(err) {
                response.CreateBadRequestResponse(w)
            } else {
                logger.Errorf("failed to update organization: %v", err)
                response.CreateInternalErrorResponse(w)
            }
            return
        }

        response.CreateSuccessResponse(org.ToResponse(), w)
    }
}

// DeleteHandlerWithDeps returns an http.HandlerFunc with injected dependencies
func DeleteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()
        vars := mux.Vars(r)
        identifier := vars["identifier"]

        // Get database connection using dependency
        connections, err := deps.GetConnections(ctx)
        if err != nil {
            logger.Errorf("failed to get connections: %v", err)
            response.CreateInternalErrorResponse(w)
            return
        }

        // Delete organization using dependency
        err = deps.DeleteOrganization(connections.Postgres, identifier)
        if err != nil {
            if IsNotFoundError(err) {
                response.CreateNotFoundResponse(w)
            } else if IsValidationError(err) {
                response.CreateNotFoundResponse(w)
            } else {
                logger.Errorf("failed to delete organization: %v", err)
                response.CreateInternalErrorResponse(w)
            }
            return
        }

        w.WriteHeader(http.StatusNoContent)
    }
}

```

## 7. Shared API Response Enhancement

### 7.1 Add Missing Response Function
**File**: `/shared/api/response/response.go`

```go
// Add this function to the existing response.go file
func CreateNotFoundResponse(w http.ResponseWriter) {
    response := map[string]interface{}{
        "status":  "error",
        "data":    nil,
        "message": "Not Found",
        "code":    http.StatusNotFound,
    }

    header := map[string]string{
        "Content-Type": "application/json",
    }

    CreateResponse(header, response, http.StatusNotFound, w)
}
```

## 8. Integration with Onramp Microservice

### 8.1 Update Onramp Routes
**File**: `/microservices/onramp/routes.go`

```go
// Add import
import (
    // ... existing imports
    RestOrganization "synapse-its.com/shared/rest/onramp/organization"
)

// Add to NewRouter function
func NewRouter(connections *connect.Connections, batch bqbatch.Batcher) *mux.Router {
    // ... existing code

    // Organization management endpoints
    router.HandleFunc("/organizations", RestOrganization.CreateHandler).Methods(http.MethodPost)
    router.HandleFunc("/organizations", RestOrganization.GetAllHandler).Methods(http.MethodGet)
    router.HandleFunc("/organizations/{identifier}", RestOrganization.GetByIdentifierHandler).Methods(http.MethodGet)
    router.HandleFunc("/organizations/{identifier}", RestOrganization.UpdateHandler).Methods(http.MethodPatch)
    router.HandleFunc("/organizations/{identifier}", RestOrganization.DeleteHandler).Methods(http.MethodDelete)

    return router
}
```

### 8.2 Update Onramp go.mod
**File**: `/microservices/onramp/go.mod`

```go
// Add dependency
require (
    // ... existing dependencies
    synapse-its.com/shared/rest v0.0.0-00010101000000-000000000000
)

// Add replace directive
replace synapse-its.com/shared/rest => /shared/rest
```

## 6. Testing with Dependency Injection

### 6.1 Handler Tests with Mocked Dependencies
**File**: `/shared/rest/onramp/organization/handler_test.go`

```go
package organization

import (
    "bytes"
    "context"
    "encoding/json"
    "net/http"
    "net/http/httptest"
    "testing"
    "time"

    "github.com/gorilla/mux"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/require"

    "synapse-its.com/shared/connect"
    "synapse-its.com/shared/mocks/dbexecutor"
)

func TestCreateHandlerWithDeps(t *testing.T) {
    t.Parallel()

    tests := []struct {
        name           string
        requestBody    interface{}
        setupMocks     func(*dbexecutor.FakeDBExecutor)
        expectedStatus int
        expectedError  bool
    }{
        {
            name: "successful_creation",
            requestBody: CreateOrganizationRequest{
                Description: "Test Organization",
            },
            setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
                mdb.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
                    org := dest.(*Organization)
                    *org = Organization{
                        ID:                     1,
                        OrganizationIdentifier: "test-uuid",
                        Description:            "Test Organization",
                        APIKey:                 "test-api-key",
                        CreatedAt:              time.Now(),
                        UpdatedAt:              time.Now(),
                        IsDeleted:              false,
                    }
                    return nil
                }
            },
            expectedStatus: http.StatusOK,
            expectedError:  false,
        },
        {
            name: "empty_description",
            requestBody: CreateOrganizationRequest{
                Description: "",
            },
            setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
                // No setup needed as validation will fail before DB call
            },
            expectedStatus: http.StatusBadRequest,
            expectedError:  true,
        },
        {
            name: "unexpected_fields",
            requestBody: map[string]interface{}{
                "description": "Test Org",
                "unexpected":  "field",
            },
            setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
                // No setup needed as parsing will fail
            },
            expectedStatus: http.StatusBadRequest,
            expectedError:  true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            t.Parallel()

            // Create mock instances
            mockDB := new(dbexecutor.FakeDBExecutor)
            tt.setupMocks(mockDB)

            // Create handler with mocked dependencies
            handler := CreateHandlerWithDeps(HandlerDeps{
                GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
                    return &connect.Connections{Postgres: mockDB}, nil
                },
                CreateOrganization: createOrganization,
            })

            // Setup request
            body, _ := json.Marshal(tt.requestBody)
            req := httptest.NewRequest(http.MethodPost, "/organizations", bytes.NewReader(body))
            w := httptest.NewRecorder()

            // Execute request
            handler(w, req)

            // Assert response
            assert.Equal(t, tt.expectedStatus, w.Code)

            if !tt.expectedError {
                var response map[string]interface{}
                err := json.Unmarshal(w.Body.Bytes(), &response)
                require.NoError(t, err)
                assert.Equal(t, "success", response["status"])
            }
        })
    }
}

// Test business logic functions directly
func TestCreateOrganization(t *testing.T) {
    t.Parallel()

    tests := []struct {
        name          string
        request       CreateOrganizationRequest
        setupMocks    func(*dbexecutor.FakeDBExecutor)
        expectedError error
    }{
        {
            name: "success",
            request: CreateOrganizationRequest{
                Description: "Test Organization",
            },
            setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
                mdb.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
                    org := dest.(*Organization)
                    *org = Organization{
                        ID:                     1,
                        OrganizationIdentifier: "test-uuid",
                        Description:            "Test Organization",
                        APIKey:                 "test-api-key",
                        CreatedAt:              time.Now(),
                        UpdatedAt:              time.Now(),
                        IsDeleted:              false,
                    }
                    return nil
                }
            },
            expectedError: nil,
        },
        {
            name: "empty_description",
            request: CreateOrganizationRequest{
                Description: "",
            },
            setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
                // No setup needed as validation will fail
            },
            expectedError: ErrInvalidDescription,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            t.Parallel()

            mockDB := new(dbexecutor.FakeDBExecutor)
            tt.setupMocks(mockDB)

            org, err := createOrganization(mockDB, tt.request)
            if tt.expectedError != nil {
                assert.ErrorIs(t, err, tt.expectedError)
                assert.Nil(t, org)
            } else {
                assert.NoError(t, err)
                assert.NotNil(t, org)
                assert.Equal(t, tt.request.Description, org.Description)
            }
        })
    }
}
```

```

## 7. Integration with Onramp Microservice

### 7.1 Update Onramp Routes
**File**: `/microservices/onramp/routes.go`

```go
// Add import
import (
    // ... existing imports
    RestOrganization "synapse-its.com/shared/rest/onramp/organization"
)

// Add to NewRouter function
func NewRouter(connections *connect.Connections, batch bqbatch.Batcher) *mux.Router {
    // ... existing code

    // Organization management endpoints
    router.HandleFunc("/organizations", RestOrganization.CreateHandler).Methods(http.MethodPost)
    router.HandleFunc("/organizations", RestOrganization.GetAllHandler).Methods(http.MethodGet)
    router.HandleFunc("/organizations/{identifier}", RestOrganization.GetByIdentifierHandler).Methods(http.MethodGet)
    router.HandleFunc("/organizations/{identifier}", RestOrganization.UpdateHandler).Methods(http.MethodPatch)
    router.HandleFunc("/organizations/{identifier}", RestOrganization.DeleteHandler).Methods(http.MethodDelete)

    return router
}
```

### 7.2 Update Onramp go.mod
**File**: `/microservices/onramp/go.mod`

```go
// Add dependency
require (
    // ... existing dependencies
    synapse-its.com/shared/rest v0.0.0-00010101000000-000000000000
)

// Add replace directive
replace synapse-its.com/shared/rest => /shared/rest
```

## 8. Key Benefits of Dependency Injection Pattern

### 8.1 Testability
- **Mocked Dependencies**: All external dependencies can be easily mocked for testing
- **Isolated Testing**: Each component can be tested in isolation
- **Comprehensive Coverage**: Business logic and HTTP handlers can be tested separately

### 8.2 Maintainability
- **Clean Separation**: Clear boundaries between HTTP handling, business logic, and data access
- **Flexible Architecture**: Easy to swap implementations without changing core logic
- **Reduced Coupling**: Components depend on interfaces, not concrete implementations

### 8.3 Production Readiness
- **Default Dependencies**: Production handlers use real implementations
- **Error Handling**: Comprehensive error handling with proper HTTP status codes
- **Logging**: Structured logging for debugging and monitoring

## 9. Implementation Summary

This implementation follows the **Domain-Nested REST API Structure with Dependency Injection** pattern established in the codebase:

### ✅ **Architecture Compliance**
- **Domain-Nested Structure**: Organized by domain (organization) within service (onramp)
- **Dependency Injection**: All external dependencies injected for testability
- **Functional Options**: Handler functions accept dependency structs
- **Clean Separation**: HTTP, business logic, and data access cleanly separated

### ✅ **Key Features**
- **5 CRUD Endpoints**: Complete REST API for organization management
- **Secure UUID Generation**: For API keys and organization identifiers
- **Input Validation**: Comprehensive validation with error handling
- **Soft Delete**: Audit trail preservation with soft delete pattern
- **Comprehensive Testing**: Mocked dependencies for thorough testing

### ✅ **Production Ready**
- **Error Handling**: Appropriate HTTP status codes and messages
- **Logging**: Structured logging for monitoring
- **Database Integration**: Uses shared connection management
- **Response Formatting**: Consistent API response structure

This pattern provides excellent testability while maintaining clean, production-ready code that follows established architectural principles.
