#!/bin/bash

# Define colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color


# Define a central directory for coverage profiles.
# This folder should be writable and mounted into your container.
COVERAGE_DIR="/app/coverage"


# ----------------------------------------------------------------
# Use AWK to parse the combined code coverage.  This is necessary because the
# built-in `go test cover` was having many, many problems with the different
# test runs and multiple programs being reported in this one test package.
# ----------------------------------------------------------------
print_file_coverage_report_union() {
  local final_cov_file="$COVERAGE_DIR/final_coverage.out"
  local report_file="$COVERAGE_DIR/final_coverage.txt"

  if [ ! -f "$final_cov_file" ]; then
    echo -e "${RED}Final coverage file not found: $final_cov_file${NC}"
    return 1
  fi

  echo -e "${YELLOW}Per-file union coverage report for $final_cov_file:${NC}"

  # First pass: Use awk to aggregate coverage data per file and print as "file|covered|total|flen".
  awk '
      $1=="mode:" { next }
    {
      key = $1
      blockStmts = $2
      blockCov   = ($3 > 0 ? blockStmts : 0)
      if (!(key in seen)) {
        seen[key]    = 1
        total[key]   = blockStmts
        covered[key] = blockCov
      } else if (covered[key] == 0 && blockCov > 0) {
        covered[key] = blockStmts
      }
    }
    END {
      # Aggregate by file.
      for (k in total) {
        split(k, parts, ":")
        file = parts[1]
        fileTotal[file]   += total[k]
        fileCovered[file] += covered[k]
      }
      # Output one line per file: file|fileCovered|fileTotal|length(file)
      for (f in fileTotal) {
        print f "|" fileCovered[f] "|" fileTotal[f] "|" length(f)
      }
    }' "$final_cov_file" > /tmp/filecov_unsorted.txt

  # Second pass: determine maximum filename length from the unsorted file.
  maxlen=$(awk -F"|" 'BEGIN { m = 0 } { if ($4 > m) m = $4 } END { print m }' \
           /tmp/filecov_unsorted.txt)

  # Third pass: sort the file alphabetically on the file name (first field).
  sort -t"|" -k1,1 /tmp/filecov_unsorted.txt > /tmp/filecov_sorted.txt

  # 3) From here on, run the loop in a single block,
  #    tee’ing the raw output to $report_file, then sed’ing it for stdout.
  {
    overallTotal=0
    overallCovered=0

    # Fourth pass: output each line with padded filename, and accumulate overall totals.
    while IFS="|" read -r file cov total flen; do
      overallTotal=$(( overallTotal + total ))
      overallCovered=$(( overallCovered + cov ))
      percent=$(awk -v c="$cov" -v t="$total" 'BEGIN{ printf "%.2f", (c/t)*100 }')
      printf "%-*s : %.2f%% (%d/%d statements)\n" \
             "$maxlen" "$file" "$percent" "$cov" "$total"
    done < /tmp/filecov_sorted.txt

    # Compute overall coverage.
    overallPercent=$(awk -v c="$overallCovered" -v t="$overallTotal" 'BEGIN{ printf "%.2f", (c/t)*100 }')
    echo "-----"
    echo "Overall coverage: $overallPercent% ($overallCovered/$overallTotal statements)"
  } | tee "$report_file" | sed 's|synapse-its.com||g'
}


# Function to merge individual coverage profiles into one final report.
merge_coverage() {
  echo -e "${YELLOW}Merging coverage reports...${NC}"

  local final="$COVERAGE_DIR/final_coverage.out"
  local pattern="$COVERAGE_DIR/partial_*_coverage.out"

  # collect files, bail if none
  shopt -s nullglob
  local files=( $pattern )
  if (( ${#files[@]} == 0 )); then
    echo -e "${RED}No coverage files found to merge.${NC}"
    return 1
  fi

  # Write the header ("mode: set") into final_coverage.out
  printf 'mode: set\n' > "$final"

  # Merge all partial files, skipping their own headers, then sort & dedupe
  tail -q -n +2 "${files[@]}" | \
    sort -t: -k1,1 -k2,2n | \
    awk '
      BEGIN { prevKey=""; prevCount=0 }
      {
        key   = $1 " " $2
        count = $3
        if (key == prevKey) {
          if (count > prevCount) prevCount = count
        } else {
          if (prevKey != "") print prevKey, prevCount
          prevKey = key; prevCount = count
        }
      }
      END {
        if (prevKey != "") print prevKey, prevCount
      }
    ' | \
    sed -E '
      s#^synapse-its\.com/shared/#/workspace/shared/#
      s#^synapse-its\.com/([^/]+)#/workspace/microservices/\1#
    ' >> "$final"

  echo -e "${GREEN}Final merged coverage report written to $final${NC}"
  print_file_coverage_report_union
}


# ----------------------------------------------------------------
# Helper function to run tests in directories matching a pattern
# in parallel.
#
# The function loops over each directory matching the pattern.
# If a directory contains a go.mod file, it launches a background
# test job (sending stdout/stderr to a temporary log file) and saves
# its PID, directory name, and log file location.
#
# After launching all jobs, it waits for each job to finish,
# checks exit codes, and if any job fails, prints its log output.
# If a failure occurs, the function returns non-zero.
# ----------------------------------------------------------------
run_tests_in_dir_pattern() {
  local pattern="$1"
  local pids=()      # Array to store background job PIDs.
  local dirs=()      # Array to store corresponding directory names.
  local logfiles=()  # Array to store temporary log file names.

  for dir in $pattern ; do
    if [ -d "$dir" ]; then
      if [ -f "$dir/go.mod" ]; then
        module_name=$(basename "$dir")
        echo -e "${YELLOW}Running go tests in $dir${NC}"
        log_file=$(mktemp)
        # Run tests silently in background.
        ( cd "$dir" && go test -covermode=set -coverprofile="$COVERAGE_DIR/partial_${module_name}_coverage.out" ./... ) >"$log_file" 2>&1 &
        pids+=($!)
        dirs+=("$dir")
        logfiles+=("$log_file")
      else
        echo -e "${GREEN}Skipping $dir (no go.mod found).${NC}"
      fi
    fi
  done

  # Wait for all jobs to finish and check exit statuses.
  local fail=false
  for i in "${!pids[@]}"; do
    pid=${pids[$i]}
    wait $pid
    status=$?
    if [ $status -ne 0 ]; then
      fail=true
      echo -e "${RED}Error encountered in go tests for ${dirs[$i]}.${NC}"
      # Only print the output if there was an error.
      cat "${logfiles[$i]}"
    fi
  done

  # Remove temporary log files.
  for logfile in "${logfiles[@]}"; do
    rm -f "$logfile"
  done

  if $fail; then
    return 1
  fi

  return 0
}


# ----------------------------------------------------------------
# Function to run integration/end-to-end tests in /app.
#
# This function runs tests for /app (your e2e/integration tests).
# Output is also captured in a temporary log file and only printed
# if the tests fail.
# ----------------------------------------------------------------
run_integration_tests() {
  echo -e "${YELLOW}Running integration/end-to-end tests${NC}"
  log_file=$(mktemp)
  ( cd /app && go test -covermode=set -coverpkg=synapse-its.com/testing/...,synapse-its.com/shared/... -coverprofile="$COVERAGE_DIR/partial_e2e_coverage.out" ./... ) > "$log_file" 2>&1
  status=$?
  if [ $status -ne 0 ]; then
    echo -e "${RED}Error encountered while running integration/end-to-end tests in /app.${NC}"
    cat "$log_file"
    rm -f "$log_file"
    return 1
  fi
  rm -f "$log_file"
  return 0
}


# ----------------------------------------------------------------
# Function to run go mod tidy and go mod download in /microservices.
#
# This function iterates through each first‑level directory under
# /microservices. For any directory containing a go.mod file, it
# runs `go mod tidy` followed by `go mod download`.
# ----------------------------------------------------------------
update_go_mod() {
  /shared/setup_shared.sh
  for svcDir in /microservices/*; do
    if [ -d "$svcDir" ] && [ -f "$svcDir/go.mod" ]; then
      (
        cd "$svcDir" || exit 1
        go mod tidy
        go mod download
      )
    fi
  done
}


# ----------------------------------------------------------------
# Main run_tests() function:
#
# Steps:
#  1. Run tests in /shared in parallel using run_tests_in_dir_pattern.
#  2. Run tests in /microservices in parallel.
#  3. If the above both succeed, then run integration/e2e tests in /app.
#
# If any step fails, this function returns non-zero.
# ----------------------------------------------------------------
run_tests() {
  # enable extglob so that !(testing) means “anything except testing”
  shopt -s extglob

  # Run Go mod update and go mod download before all tests
  update_go_mod

  # Step 1 & 2 in parallel, excluding microservices/testing
  run_tests_in_dir_pattern "/shared/!(protobuf-schemas)/"   &
  pid_shared=$!

  run_tests_in_dir_pattern "/microservices/!(testing)/" &
  pid_micro=$!

  # wait for shared; if it fails, bubble up non‑zero
  wait $pid_shared || return 1
  # then wait for microservices
  wait $pid_micro  || return 1

  # Step 3: integration / e2e
  run_integration_tests || return 1

  # Finally merge coverage
  merge_coverage

  return 0
}


# ----------------------------------------------------------------
# Adds the appropriate `go mod replace` directives so that all modules can be
# found by the tests.
# ----------------------------------------------------------------
set_go_mod_replaces() {
  # For all module directories in /microservices and /shared.
  for base in /microservices /shared; do
    for module_dir in "$base"/*/; do
      if [ -d "$module_dir" ] && [ -f "${module_dir}go.mod" ]; then
        modName=$(grep '^module ' "${module_dir}go.mod" | sed -E 's/module[[:space:]]+//')
        modPath=$(realpath "$module_dir")
        if [ -n "$modName" ]; then
          # echo "Setting go mod replace: ${modName} -> ${modPath}"
          # go mod edit -replace "${modName}=${modPath}"
          GUARD="replace ${modName} => ${modPath}"
            if ! grep -qF "${GUARD}" go.mod; then 
              # echo "DEBUG: adding replace ${moduleName}=>${d}"
              go mod edit -replace "${modName}=${modPath}"
            fi
        fi
      fi
    done
  done
}


# ----------------------------------------------------------------
# Main loop: run tests and then poll every second for file changes.
# ----------------------------------------------------------------
echo -e "${GREEN}Starting test cycle...${NC}"

# Setup modules.
set_go_mod_replaces
go mod tidy
go mod download

# Delete any old coverage files.
rm -f "$COVERAGE_DIR"/*_coverage.out
rm -f "$COVERAGE_DIR"/cloc_report.txt

cloc --quiet --hide-rate /app /shared /microservices /schemas > $COVERAGE_DIR/cloc_report.txt 2>&1 &
cloc_pid=$!

# Run tests.
run_tests
cycle_status=$?
if [ $cycle_status -eq 0 ]; then
  echo -e "${GREEN}Test cycle complete (all tests passed).${NC}"
else
  echo -e "${RED}Test cycle failed.${NC}"
fi

# Additional stats.
wait $cloc_pid
cat $COVERAGE_DIR/cloc_report.txt
