// src/app/auth.guard.ts
import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { OAuthService } from 'angular-oauth2-oidc';

@Injectable({ providedIn: 'root' })
export class AuthGuard implements CanActivate {
  constructor(private oauth: OAuthService, private router: Router) { }

  canActivate(): boolean {
    if (this.oauth.hasValidAccessToken()) {
      return true;
    }
    this.oauth.initLoginFlow();
    return false;
  }
}
