import { Component, OnInit } from '@angular/core';
import { OAuthService } from 'angular-oauth2-oidc';

@Component({
  selector: 'app-home',
  standalone: false,
  templateUrl: './home.component.html',
  styleUrl: './home.component.css'
})
export class HomeComponent implements OnInit {
  // make the service public so the template can see it
  constructor(public oauth: OAuthService) { }

  ngOnInit(): void {
    // you can also inspect the current login state here if you like
    console.log('Logged in?', this.oauth.hasValidAccessToken());
  }
}
