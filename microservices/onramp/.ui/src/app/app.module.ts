import { NgModule, APP_INITIALIZER } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { HttpClientModule, provideHttpClient } from '@angular/common/http';

import { AppComponent } from './app.component';
import { FooListComponent } from './foo-list/foo-list.component';
import { provideNzI18n } from 'ng-zorro-antd/i18n';
import { en_US } from 'ng-zorro-antd/i18n';
import { registerLocaleData } from '@angular/common';
import en from '@angular/common/locales/en';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { OrganizationsComponent } from './organizations/organizations.component';

import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { EditAddComponent } from './organizations/edit-add/edit-add.component';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { IconDirective } from '@ant-design/icons-angular';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';

registerLocaleData(en);

import { OAuthModule, OAuthService, AuthConfig } from 'angular-oauth2-oidc';
import { environment } from '../environments/environments';
import { ProtectedComponent } from './protected/protected.component';
import { RouterModule } from '@angular/router';
import { AppRoutingModule } from './app-routing.module';
import { HomeComponent } from './home/<USER>';
import { HeaderComponent } from './header/header.component';
import { MenuComponent } from './menu/menu.component';

export function initAuth(oauth: OAuthService) {
  return () => {
    const cfg: AuthConfig = {
      issuer: environment.oidc.issuer,
      clientId: environment.oidc.clientId,
      redirectUri: environment.oidc.redirectUri,
      scope: environment.oidc.scope,
      responseType: environment.oidc.responseType,
      strictDiscoveryDocumentValidation: false,
      requireHttps: false,
    };
    oauth.configure(cfg);
    return oauth.loadDiscoveryDocumentAndTryLogin();
  };
}

@NgModule({
  declarations: [
    AppComponent,
    ProtectedComponent,
    HomeComponent,
    OrganizationsComponent,
    EditAddComponent,
    HeaderComponent,
    MenuComponent,
  ],
  imports: [
    BrowserModule,
    HttpClientModule,
    FooListComponent,
    OAuthModule.forRoot(),
    RouterModule,
    AppRoutingModule,
    FormsModule,
    NzTableModule,
    NzButtonModule,
    NzModalModule,
    NzFormModule,
    ReactiveFormsModule,
    NzDividerModule,
    NzInputModule,
    NzIconModule,
    NzMenuModule,
    IconDirective,
    NzLayoutModule,
    NzSelectModule,
    NzAvatarModule,
  ],
  providers: [
    {
      provide: APP_INITIALIZER,
      useFactory: initAuth,
      deps: [OAuthService],
      multi: true
    },
    provideNzI18n(en_US),
    provideAnimationsAsync(),
    provideHttpClient()
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
