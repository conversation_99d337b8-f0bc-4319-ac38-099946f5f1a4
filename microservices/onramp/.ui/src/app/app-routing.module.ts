// src/app/app-routing.module.ts
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ProtectedComponent } from './protected/protected.component';
import { AuthGuard } from './auth.guard';
import { HomeComponent } from './home/<USER>';
import { OrganizationsComponent } from './organizations/organizations.component';
import { FooListComponent } from './foo-list/foo-list.component';

const routes: Routes = [
  { path: '', component: HomeComponent },
  { path: 'protected', component: ProtectedComponent, canActivate: [AuthGuard] },
  { path: 'organizations', component: OrganizationsComponent },
  { path: 'foo', component: FooListComponent },
  { path: '**', redirectTo: 'home' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
