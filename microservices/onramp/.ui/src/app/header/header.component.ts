import { Component } from '@angular/core';
import { OAuthService } from 'angular-oauth2-oidc';

@Component({
  selector: 'app-header',
  standalone: false,
  templateUrl: './header.component.html',
  styleUrl: './header.component.css'
})
export class HeaderComponent {

  userName: string | null = null;
  constructor(public oauthService: OAuthService) {
    const claims = this.oauthService.getIdentityClaims();
    this.userName = claims ? (claims as any).name : null;
  }
  login() {
    this.oauthService.initLoginFlow();  // redirects to Keycloak
  }
  logout() {
    this.oauthService.logOut();
  }
}
