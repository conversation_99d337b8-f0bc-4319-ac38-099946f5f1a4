# ─────────────────────────────────────────────────────────────────────────────
# Stage 1: Backend builder (Go) ─ compile the Go onramp microservice
# ─────────────────────────────────────────────────────────────────────────────
FROM golang:1.24 AS backend-builder
WORKDIR /app

# build metadata
ARG GIT_BRANCH=unknown
ARG GIT_TAG=untagged

# copy backend code & shared libraries
COPY microservices/onramp ./
COPY shared /shared
RUN chmod +x /shared/setup_shared.sh

# wire up shared modules + download deps
RUN /shared/setup_shared.sh \
 && go mod tidy \
 && go mod download

# embed branch–tag into a file
RUN echo "${GIT_BRANCH}-${GIT_TAG}" > identifier.txt

# compile
RUN go build -o onramp

# ─────────────────────────────────────────────────────────────────────────────
# Stage 2: UI builder (Angular) ─ compiles the front-end into static assets
# ─────────────────────────────────────────────────────────────────────────────
FROM node:24 AS ui-builder
WORKDIR /app

# install dependencies
COPY microservices/onramp/.ui/package*.json ./
RUN npm ci

# copy the rest of your Angular code and build
# NOTE: the package.json has specified that this will be a production build.
COPY microservices/onramp/.ui/ .
RUN npm run build

# ─────────────────────────────────────────────────────────────────────────────
# Stage 3: Production image (distroless) ─ ships only the Go binary + static UI
# ─────────────────────────────────────────────────────────────────────────────
FROM gcr.io/distroless/base-debian12 AS prod
WORKDIR /prod

# copy the Go binary + metadata
COPY --from=backend-builder /app/onramp .
COPY --from=backend-builder /app/identifier.txt .

# copy compiled Angular into ./static
COPY --from=ui-builder /app/dist/onramp ./static

# serve APIs AND the static Angular app under /
ENTRYPOINT ["./onramp"]

# ─────────────────────────────────────────────────────────────────────────────
# Stage 4: Development image ─ one container for both ng serve + go run
# ─────────────────────────────────────────────────────────────────────────────
FROM golang:1.24 AS dev

# install Node + Angular CLI
RUN apt-get update && apt-get install -y curl ca-certificates \
 && curl -fsSL https://deb.nodesource.com/setup_24.x | bash - \
 && apt-get install -y nodejs \
 && npm install -g @angular/cli

WORKDIR /app

# entrypoint: run setup_shared.sh, then:
#  - start the Go API
#  - in parallel, cd ui && ng serve on :4200
CMD sh -c "\
  /shared/setup_shared.sh && \
  go mod tidy && go mod download && \
  cd /app && go run . & \
  cd /app/.ui && npm config set fund false && npm install && npm run start -- --host 0.0.0.0 --disable-host-check --poll 1500 --hmr \
"

# ─────────────────────────────────────────────────────────────────────────────
# Stage 5: Cucumber testing image
# ─────────────────────────────────────────────────────────────────────────────
FROM node:24 AS cucumber
WORKDIR /e2e

ENV BASE_URL=http://onramp:4200
ENV PATH=/e2e/node_modules/.bin:$PATH

CMD sh -c "\
  npm config set fund false && \
  npm ci && \
  wait-on \
    http-get://onramp:8081/readyz \
    http-get://onramp:4200 \
    http-get://keycloak:8080/realms/onramp-dev \
    http-get://selenium:4444/status && \
  cucumber-js --retry 0"
