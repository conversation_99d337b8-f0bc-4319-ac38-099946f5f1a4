package faultLogs

import (
	"context"
	"errors"
	"testing"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"cloud.google.com/go/pubsub"
	"github.com/google/uuid"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	mocks "synapse-its.com/shared/mocks"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// Mock translater functions
func mockLogMonitorResetTranslater(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte) (*edihelper.LogMonitorResetRecords, *edihelper.HeaderRecord, error) {
	return &edihelper.LogMonitorResetRecords{}, &edihelper.HeaderRecord{}, nil
}

func mockLogPreviousFailTranslater(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte) (*edihelper.LogPreviousFailRecords, *edihelper.HeaderRecord, error) {
	return &edihelper.LogPreviousFailRecords{}, &edihelper.HeaderRecord{}, nil
}

func mockLogACLineEventTranslater(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte) (*edihelper.LogACLineEventRecords, *edihelper.HeaderRecord, error) {
	return &edihelper.LogACLineEventRecords{}, &edihelper.HeaderRecord{}, nil
}

func mockFaultSignalSequenceTranslater(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte) (*edihelper.FaultSignalSequenceRecords, *edihelper.HeaderRecord, error) {
	return &edihelper.FaultSignalSequenceRecords{}, &edihelper.HeaderRecord{}, nil
}

func mockConfigurationChangeLogTranslater(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte) (*edihelper.ConfigurationChangeLogRecords, *edihelper.HeaderRecord, error) {
	return &edihelper.ConfigurationChangeLogRecords{}, &edihelper.HeaderRecord{}, nil
}

// Mock BQ converter functions
func mockLogMonitorResetBQConverter(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.LogMonitorResetTranslater, loguuid string) edihelper.BQConverter {
	return func(byteMsg []byte) (any, error) {
		return nil, nil
	}
}

func mockLogPreviousFailBQConverter(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.LogPreviousFailTranslater, loguuid string) edihelper.BQConverter {
	return func(byteMsg []byte) (any, error) {
		return nil, nil
	}
}

func mockLogACLineEventBQConverter(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.LogACLineEventTranslater, loguuid string) edihelper.BQConverter {
	return func(byteMsg []byte) (any, error) {
		return nil, nil
	}
}

func mockLogFaultSignalSequenceBQConverter(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.FaultSignalSequenceTranslater, existingSeq *schemas.LogFaultSignalSequence, loguuid string) edihelper.BQConverter {
	return func(byteMsg []byte) (any, error) {
		return nil, nil
	}
}

func mockLogConfigurationBQConverter(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.ConfigurationChangeLogTranslater, loguuid string) edihelper.BQConverter {
	return func(byteMsg []byte) (any, error) {
		return nil, nil
	}
}

func TestHandlerWithDeps(t *testing.T) {
	tests := []struct {
		name         string
		connectorErr error
		receiveErr   error
		attrErr      error
		unmarshalErr error
		logs         []*gatewayv1.LogEntry
		batchErr     error
		batchAddErr  error
		sendDlqErr   error

		wantDLQ  int
		wantAdds int
	}{
		{name: "Connector error", connectorErr: errors.New("no conn"), wantDLQ: 0, wantAdds: 0},
		{name: "Receive error", receiveErr: errors.New("recv fail"), wantDLQ: 0, wantAdds: 0},
		{name: "Attribute unmarshal error", attrErr: errors.New("fail attr parse"), wantDLQ: 1, wantAdds: 0},
		{name: "Unmarshal error", unmarshalErr: errors.New("bad proto"), wantDLQ: 1, wantAdds: 0},
		{name: "GetBatch error", logs: []*gatewayv1.LogEntry{{LogType: "LogMonitorReset", Message: [][]byte{[]byte("x")}}}, batchErr: errors.New("no batch"), wantDLQ: 0, wantAdds: 0},
		{name: "Batch Add error", logs: []*gatewayv1.LogEntry{{LogType: "LogMonitorReset", Message: [][]byte{[]byte("y")}}}, batchAddErr: errors.New("add fail"), wantDLQ: 1, wantAdds: 0},
		{
			name: "Happy path - Multiple log types",
			logs: []*gatewayv1.LogEntry{
				{LogType: "LogMonitorReset", Message: [][]byte{[]byte("reset")}},
				{LogType: "LogPreviousFail", Message: [][]byte{[]byte("fail")}},
				{LogType: "LogACLineEvent", Message: [][]byte{[]byte("ac")}},
				{LogType: "LogFaultSignalSequence", Message: [][]byte{[]byte("fault")}},
				{LogType: "LogConfiguration", Message: [][]byte{[]byte("config")}},
			},
			wantDLQ:  0,
			wantAdds: 6, // initial Add existingSeq under current logic
		},
		{
			name: "ExistingSeq appended for FaultSignalSequence",
			logs: []*gatewayv1.LogEntry{
				{LogType: "LogFaultSignalSequence", Message: [][]byte{[]byte("fault1")}},
			},
			wantDLQ:  0,
			wantAdds: 2, // initial Add existingSeq appended
		},
		// 1) ParseAttributes failure → SendToDLQ fails → nested logger msg.Nack
		{
			name:       "ParseAttributes error DLQ failure",
			attrErr:    errors.New("fail attr parse"),
			sendDlqErr: errors.New("dlq send fail"),
			wantDLQ:    0, // we never increment on DLQ-failure
			wantAdds:   0, // and we bail out before any Adds
		},

		// 2) UnmarshalDeviceLogs failure → SendToDLQ fails → nested logger msg.Nack
		{
			name:         "Unmarshal error DLQ failure",
			unmarshalErr: errors.New("bad proto"),
			sendDlqErr:   errors.New("dlq send fail"),
			wantDLQ:      0,
			wantAdds:     0,
		},

		// 3) Initial batch.Add(faultLogs) error → SendToDLQ fails → nested logger msg.Nack
		{
			name:        "Initial batch.Add error DLQ failure",
			logs:        []*gatewayv1.LogEntry{{LogType: "LogMonitorReset", Message: [][]byte{[]byte("y")}}},
			batchAddErr: errors.New("add fail"),
			sendDlqErr:  errors.New("dlq send fail"),
			wantDLQ:     0,
			wantAdds:    0,
		},
	}

	for _, tc := range tests {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			ctx := context.Background()
			dlqCount := 0
			addCount := 0

			// Setup fake connections and batcher
			conn := mocks.FakeConns()
			client := conn.Pubsub.(*mocks.FakePubsubClient)
			client.ReceiveError = tc.receiveErr

			fakeBatch, _ := mocks.FakeBatch(ctx)
			fakeBatch.(*mocks.FakeBatcher).AddFn = func(row interface{}) error {
				if tc.batchAddErr != nil {
					return tc.batchAddErr
				}
				addCount++
				return nil
			}
			logUUID := uuid.New().String()
			existingSeq := schemas.LogFaultSignalSequence{}
			// Build HandlerDeps
			deps := HandlerDeps{
				Connector: ConnectorFunc(func(ctx context.Context, _ ...bool) (*connect.Connections, error) { return conn, tc.connectorErr }),
				SendToDLQ: DLQSender(func(ctx context.Context, c connect.PsClient, msg *pubsub.Message, reason string) error {
					if tc.sendDlqErr != nil {
						return tc.sendDlqErr
					}
					dlqCount++
					return nil
				}),
				UnmarshalLogs: UnmarshalDeviceLogsFunc(func(raw []byte) (*gatewayv1.DeviceLogs, error) {
					if tc.unmarshalErr != nil {
						return nil, tc.unmarshalErr
					}
					return &gatewayv1.DeviceLogs{DeviceId: "test-device", Logs: tc.logs}, nil
				}),
				GetBatch: BatchGetter(func(ctx context.Context) (bqbatch.Batcher, error) {
					if tc.batchErr != nil {
						return nil, tc.batchErr
					}
					return fakeBatch, nil
				}),
				ParseAttributes: ParseAttributesFunc(func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
					if tc.attrErr != nil {
						return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, tc.attrErr
					}
					return pubsubdata.CommonAttributes{OrganizationIdentifier: "test-org", Topic: "test-topic"}, pubsubdata.HeaderDetails{GatewayDeviceID: "test-gateway", GatewayTimezone: "UTC"}, nil
				}),
				LogMonitorResetBQConverterConstructor: func(
					httpHeader *pubsubdata.HeaderDetails,
					commonAttrs *pubsubdata.CommonAttributes,
					pubsubMessage *pubsub.Message,
					translater edihelper.LogMonitorResetTranslater,
					uuid string,
				) edihelper.BQConverter {
					return mockLogMonitorResetBQConverter(httpHeader, commonAttrs, pubsubMessage, mockLogMonitorResetTranslater, logUUID)
				},
				LogPreviousFailBQConverterConstructor: func(
					httpHeader *pubsubdata.HeaderDetails,
					commonAttrs *pubsubdata.CommonAttributes,
					pubsubMessage *pubsub.Message,
					translater edihelper.LogPreviousFailTranslater,
					uuid string,
				) edihelper.BQConverter {
					return mockLogPreviousFailBQConverter(httpHeader, commonAttrs, pubsubMessage, mockLogPreviousFailTranslater, logUUID)
				},
				LogACLineEventBQConverterConstructor: func(
					httpHeader *pubsubdata.HeaderDetails,
					commonAttrs *pubsubdata.CommonAttributes,
					pubsubMessage *pubsub.Message,
					translater edihelper.LogACLineEventTranslater,
					uuid string,
				) edihelper.BQConverter {
					return mockLogACLineEventBQConverter(httpHeader, commonAttrs, pubsubMessage, mockLogACLineEventTranslater, logUUID)
				},
				LogFaultSignalSequenceBQConverterConstructor: func(
					httpHeader *pubsubdata.HeaderDetails,
					commonAttrs *pubsubdata.CommonAttributes,
					pubsubMessage *pubsub.Message,
					translater edihelper.FaultSignalSequenceTranslater,
					existing *schemas.LogFaultSignalSequence,
					uuid string,
				) edihelper.BQConverter {
					// simulate setting existingSeq for test branch
					if existing != nil {
						existing.OrganizationIdentifier = "test-org"
					}
					return mockLogFaultSignalSequenceBQConverter(httpHeader, commonAttrs, pubsubMessage, mockFaultSignalSequenceTranslater, &existingSeq, logUUID)
				},
				LogConfigurationBQConverterConstructor: func(
					httpHeader *pubsubdata.HeaderDetails,
					commonAttrs *pubsubdata.CommonAttributes,
					pubsubMessage *pubsub.Message,
					translater edihelper.ConfigurationChangeLogTranslater,
					uuid string,
				) edihelper.BQConverter {
					return mockLogConfigurationBQConverter(httpHeader, commonAttrs, pubsubMessage, mockConfigurationChangeLogTranslater, logUUID)
				},
			}

			// Publish test message if no connector or receive error
			if tc.connectorErr == nil && tc.receiveErr == nil {
				msg := &pubsub.Message{
					Data:        []byte{1, 2, 3},
					ID:          "id",
					PublishTime: time.Now(),
					Attributes: map[string]string{
						"organizationIdentifier": "test-org",
						"topic":                  "test-topic",
					},
				}

				topic := client.Topic("topic")
				topic.Publish(ctx, msg)
				client.CreateSubscription(ctx, "sub", connect.SubscriptionConfig{Topic: topic})
			}

			// Invoke handler
			h := HandlerWithDeps(deps)
			h(ctx, "sub")

			// Verify counts
			if dlqCount != tc.wantDLQ {
				t.Errorf("DLQ calls = %d; want %d", dlqCount, tc.wantDLQ)
			}
			if addCount != tc.wantAdds {
				t.Errorf("Batch adds = %d; want %d", addCount, tc.wantAdds)
			}
		})
	}
}

// Test that the existingSeq path appends the sequence when OrganizationIdentifier is set
func TestHandlerWithDeps_ExistingSeqAppended(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	addCount := 0
	dlqCount := 0

	// Setup fake connections and batcher
	conn := mocks.FakeConns()
	client := conn.Pubsub.(*mocks.FakePubsubClient)
	// No receive error

	fakeBatch, _ := mocks.FakeBatch(ctx)
	fakeBatch.(*mocks.FakeBatcher).AddFn = func(row interface{}) error {
		addCount++
		return nil
	}

	// Build deps with converter that sets existingSeq.OrganizationIdentifier
	deps := HandlerDeps{
		Connector: ConnectorFunc(func(ctx context.Context, _ ...bool) (*connect.Connections, error) { return conn, nil }),
		SendToDLQ: DLQSender(func(ctx context.Context, c connect.PsClient, msg *pubsub.Message, reason string) error {
			dlqCount++
			return nil
		}),
		UnmarshalLogs: UnmarshalDeviceLogsFunc(func(raw []byte) (*gatewayv1.DeviceLogs, error) {
			return &gatewayv1.DeviceLogs{
				DeviceId: "test-device",
				Logs: []*gatewayv1.LogEntry{{
					LogType: "LogFaultSignalSequence",
					Message: [][]byte{[]byte("fault")},
				}},
			}, nil
		}),
		GetBatch: BatchGetter(func(ctx context.Context) (bqbatch.Batcher, error) { return fakeBatch, nil }),
		ParseAttributes: ParseAttributesFunc(func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
			return pubsubdata.CommonAttributes{OrganizationIdentifier: "test-org", Topic: "test-topic"}, pubsubdata.HeaderDetails{}, nil
		}),
		LogMonitorResetBQConverterConstructor: mockLogMonitorResetBQConverter,
		LogPreviousFailBQConverterConstructor: mockLogPreviousFailBQConverter,
		LogACLineEventBQConverterConstructor:  mockLogACLineEventBQConverter,
		LogFaultSignalSequenceBQConverterConstructor: func(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.FaultSignalSequenceTranslater, existing *schemas.LogFaultSignalSequence, uuid string) edihelper.BQConverter {
			return func(byteMsg []byte) (any, error) {
				existing.OrganizationIdentifier = commonAttrs.OrganizationIdentifier
				return nil, nil
			}
		},
		LogConfigurationBQConverterConstructor: mockLogConfigurationBQConverter,
	}

	// Publish a single message containing only the fault signal sequence log
	msg := &pubsub.Message{Data: []byte{1}, ID: "id", PublishTime: time.Now(), Attributes: map[string]string{"organizationIdentifier": "test-org", "topic": "test-topic"}}
	topic := client.Topic("topic")
	topic.Publish(ctx, msg)
	client.CreateSubscription(ctx, "sub", connect.SubscriptionConfig{Topic: topic})

	// Invoke handler
	h := HandlerWithDeps(deps)
	h(ctx, "sub")

	// We expect two adds: one for the full faultLogs, one for existingSeq
	if addCount != 2 {
		t.Errorf("Expected 2 batch adds (faultLogs existingSeq), got %d", addCount)
	}
}

func Test_createIndividualLogBQItems(t *testing.T) {
	tests := []struct {
		name           string
		rawLogMessages [][]byte
		converter      edihelper.BQConverter
		want           []any
	}{
		{name: "Success - All messages converted", rawLogMessages: [][]byte{[]byte("message1"), []byte("message2")}, converter: func(byteMsg []byte) (any, error) { return string(byteMsg), nil }, want: []any{"message1", "message2"}},
		{name: "Success - Empty messages", rawLogMessages: [][]byte{}, converter: func(byteMsg []byte) (any, error) { return string(byteMsg), nil }, want: []any{}},
		{name: "Error - Some messages fail conversion", rawLogMessages: [][]byte{[]byte("message1"), []byte("error"), []byte("message3")}, converter: func(byteMsg []byte) (any, error) {
			if string(byteMsg) == "error" {
				return nil, errors.New("conversion error")
			}
			return string(byteMsg), nil
		}, want: []any{"message1", "message3"}},
		{name: "Error - All messages fail conversion", rawLogMessages: [][]byte{[]byte("error1"), []byte("error2")}, converter: func(byteMsg []byte) (any, error) { return nil, errors.New("conversion error") }, want: []any{}},
	}

	for _, tc := range tests {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			got := createIndividualLogBQItems(tc.rawLogMessages, tc.converter)
			if len(got) != len(tc.want) {
				t.Errorf("createIndividualLogBQItems() returned %d items, want %d", len(got), len(tc.want))
				return
			}
			for i := range got {
				if got[i] != tc.want[i] {
					t.Errorf("createIndividualLogBQItems()[%d] = %v, want %v", i, got[i], tc.want[i])
				}
			}
		})
	}
}
