package perfStats

import (
	"context"
	"fmt"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"cloud.google.com/go/pubsub"
	"synapse-its.com/etl/processors/handlers/etlShared"
	"synapse-its.com/etl/processors/handlers/gateway/helper"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// --- Dependency abstractions for injection and testing ---
type (
	ConnectorFunc            func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ParseAttributesFunc      func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error)
	DLQSender                func(ctx context.Context, client connect.PsClient, msg *pubsub.Message, reason string) error
	UnmarshalGatewayLogsFunc func(raw []byte) (*gatewayv1.GatewayLogs, error)
	BatchGetter              func(ctx context.Context) (bqbatch.Batcher, error)
	ToBQConverter            func(orgID string, sgwID string, tz string, topic string, pubsubID string, pubsubTS time.Time, messageTime time.Time, softwareGatewayDeviceID string, count int64, lastExecutedTime time.Time, lastExecutedElapsedTime int64, totalTime int64, minTime int64, maxTime int64, errorCount int64, rawMessage []byte) schemas.GatewayPerformanceStatistics
	UnmarshalCompressedBytes func(compressedData []byte, v any) error
)

// HandlerDeps bundles all external dependencies.
type HandlerDeps struct {
	Connector                ConnectorFunc
	ParseAttributes          ParseAttributesFunc
	SendToDLQ                DLQSender
	UnmarshalGatewayLogs     UnmarshalGatewayLogsFunc
	GetBatch                 BatchGetter
	ToBQ                     ToBQConverter
	UnmarshalCompressedBytes UnmarshalCompressedBytes
}

func HandlerWithDeps(deps HandlerDeps) func(ctx context.Context, subscriptionName string) {
	return func(ctx context.Context, subscriptionName string) {
		batch, batchErr := deps.GetBatch(ctx)
		if batchErr != nil {
			logger.Errorf("Error getting batch: %v", batchErr)
			return
		}
		connections, err := deps.Connector(ctx)
		if err != nil {
			logger.Errorf("Error getting connections%v", err)
			return
		}
		sub := connections.Pubsub.Subscription(subscriptionName)
		err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
			logger.Debugf("Received message on %s MessageID: %s. Message Data:%s", subscriptionName, string(msg.ID), string(msg.Data))

			// Parse Attributes
			commonAttrs, httpHeader, errPa := deps.ParseAttributes(msg.Attributes)
			if errPa != nil {
				logger.Errorf("Unable to parse attributes: %v", msg.Attributes)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Unable to parse attributes: %v", errPa))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Unmarshall protobuf message
			gatewayPerfStats, errUm := deps.UnmarshalGatewayLogs(msg.Data)
			if errUm != nil {
				logger.Errorf("Error Unmarshaling protobuf message: %v", errUm)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Error Unmarshaling the Device Data: %v", errUm))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Unmarshal the compressed data into a Stats struct
			stats := edihelper.Stats{}
			err = deps.UnmarshalCompressedBytes(gatewayPerfStats.GetMessage(), &stats)
			if err != nil {
				logger.Errorf("Error Unmarshaling compressed data: %v", err)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Error Unmarshaling the Device Data: %v", err))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Add to the bigquery insert batch
			bqItem := deps.ToBQ(
				commonAttrs.OrganizationIdentifier,
				httpHeader.GatewayDeviceID,
				httpHeader.GatewayTimezone,
				commonAttrs.Topic,
				msg.ID,
				msg.PublishTime.UTC(),
				gatewayPerfStats.GetMessageTime().AsTime(),
				commonAttrs.DeviceType,
				stats.Count,
				stats.LastExceutedTimeUTC,
				stats.LastExecutedElapsedTimeMS,
				stats.TotalTimeMS,
				stats.MinTimeMS,
				stats.MaxTimeMS,
				stats.ErrorCount,
				gatewayPerfStats.GetMessage(),
			)
			if err = batch.Add(bqItem); err != nil {
				logger.Errorf("Error adding message to batch: %v", err)
				msg.Nack()
				return
			}

			msg.Ack()
		})
		if err != nil {
			logger.Errorf("Failed to receive messages from subscription"+subscriptionName+": %v", err)
		}
	}
}

// Handler is the production-ready Pub/Sub processor using real dependencies.
var Handler = HandlerWithDeps(HandlerDeps{
	Connector:                connect.GetConnections,
	ParseAttributes:          pubsubdata.ParseAttributes,
	SendToDLQ:                etlShared.SendToDLQ,
	UnmarshalGatewayLogs:     etlShared.UnmarshalGatewayLogs,
	GetBatch:                 bqbatch.GetBatch,
	ToBQ:                     edihelper.PerfStatsToBQ,
	UnmarshalCompressedBytes: helper.UnmarshalCompressedBytes,
})
