package gateway

import (
	"context"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
)

func TestUseDefaultStrategy(t *testing.T) {
	ctx := context.Background()
	conns := mocks.FakeConns()
	ctx = connect.WithConnections(ctx, conns)

	// Patch connect.GetConnections to use our test context
	origGetConnections := connect.GetConnections
	connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
		return conns, nil
	}
	defer func() { connect.GetConnections = origGetConnections }()

	topic := conns.Pubsub.Topic("test-topic").(*mocks.FakePubsubTopic)
	conns.Pubsub.CreateSubscription(ctx, "test-sub", connect.SubscriptionConfig{Topic: topic})
	topic.Publish(ctx, &pubsub.Message{Data: []byte("fault-message-1")})
	topic.Publish(ctx, &pubsub.Message{Data: []byte("fault-message-2")})

	done := make(chan struct{})
	go func() {
		UseDefaultStrategy(ctx, "test-sub")
		close(done)
	}()

	select {
	case <-done:
	case <-time.After(1 * time.Second):
		t.Fatal("BaseHandler did not complete in time")
	}
	// No assertion here as the function only logs and acks, but this ensures no panic and messages are processed.
}
