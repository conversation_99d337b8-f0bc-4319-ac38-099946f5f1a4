package gateway

import (
	"context"

	"cloud.google.com/go/pubsub"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// UseDefaultStrategy is the default strategy for handling gateway messages
func UseDefaultStrategy(ctx context.Context, subscriptionName string) {
	connections, err := connect.GetConnections(ctx)
	if err != nil {
		logger.Errorf("Error getting connections%v", err)
	}
	sub := connections.Pubsub.Subscription(subscriptionName)
	err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
		logger.Debugf("Received message on %s: %s\n", subscriptionName, string(msg.Data))
		msg.Ack()
	})
	if err != nil {
		logger.Errorf("Failed to receive messages from subscription"+subscriptionName+": %v", err)
	}
}
