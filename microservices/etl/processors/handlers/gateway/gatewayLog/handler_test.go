package gatewayLog

import (
	"context"
	"errors"
	"testing"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
	"synapse-its.com/etl/processors/handlers/etlShared"
	"synapse-its.com/shared/api/helper"
	"synapse-its.com/shared/bqbatch"
	connect "synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// Mock implementations
type mockConnector struct {
	mock.Mock
}

func (m *mockConnector) GetConnections(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
	args := m.Called(ctx, checkConnections)
	return args.Get(0).(*connect.Connections), args.Error(1)
}

type mockPubsubClient struct {
	mock.Mock
	connect.PsClient
}

func (m *mockPubsubClient) Subscription(name string) connect.PsSubscription {
	args := m.Called(name)
	return args.Get(0).(connect.PsSubscription)
}

type mockSubscription struct {
	mock.Mock
	connect.PsSubscription
}

func (m *mockSubscription) Receive(ctx context.Context, f func(context.Context, *pubsub.Message)) error {
	args := m.Called(ctx, f)
	return args.Error(0)
}

// createTestMessage creates a test message with the given log message
func createTestMessage(t *testing.T, logMessage string) *pubsub.Message {
	// Compress the log message
	compressedLog, err := helper.CompressBytes([]byte(logMessage))
	if err != nil {
		t.Fatal(err)
	}

	// Create test gateway logs
	gatewayLogs := &gatewayv1.GatewayLogs{
		Message:     compressedLog,
		MessageTime: timestamppb.New(time.Now()),
	}
	gatewayLogsBytes, err := proto.Marshal(gatewayLogs)
	if err != nil {
		t.Fatal(err)
	}

	return &pubsub.Message{
		ID:   "test-msg-id",
		Data: gatewayLogsBytes,
		Attributes: map[string]string{
			"organizationIdentifier": "test-org",
			"topic":                  "test-topic",
			"deviceType":             "test-device",
		},
		PublishTime: time.Now(),
	}
}

func TestHandlerWithDeps(t *testing.T) {
	ctx := context.Background()

	cases := []struct {
		name          string
		connErr       error
		recvErr       error
		attrErr       error
		unmarshalErr  error
		batchErr      error
		batchAddErr   error
		dlqErr        error
		decompressErr error
		wantDLQ       int
		wantBatchAdds int
	}{
		{
			name:     "get batch error",
			batchErr: errors.New("no batch"),
			wantDLQ:  0, wantBatchAdds: 0,
		},
		{
			name:    "connector error",
			connErr: errors.New("no conn"),
			wantDLQ: 0, wantBatchAdds: 0,
		},
		{
			name:    "receive error",
			recvErr: errors.New("recv fail"),
			wantDLQ: 0, wantBatchAdds: 0,
		},
		{
			name:    "parse attributes error",
			attrErr: errors.New("fail attr parse"),
			wantDLQ: 1, wantBatchAdds: 0,
		},
		{
			name:    "parse attributes error and fail to send to DLQ",
			attrErr: errors.New("fail attr parse"),
			dlqErr:  errors.New("dlq failed"),
			wantDLQ: 0, wantBatchAdds: 0,
		},
		{
			name:         "unmarshal protobuf message error",
			unmarshalErr: errors.New("bad proto"),
			wantDLQ:      1, wantBatchAdds: 0,
		},
		{
			name:         "unmarshal protobuf message error and fail to send to DLQ",
			unmarshalErr: errors.New("bad proto"),
			dlqErr:       errors.New("dlq failed"),
			wantDLQ:      0, wantBatchAdds: 0,
		},
		{
			name:          "decompress error",
			decompressErr: errors.New("decompress failed"),
			wantDLQ:       1, wantBatchAdds: 0,
		},
		{
			name:          "decompress error and fail to send to DLQ",
			decompressErr: errors.New("decompress failed"),
			dlqErr:        errors.New("dlq failed"),
			wantDLQ:       0, wantBatchAdds: 0,
		},
		{
			name:        "batch add error",
			batchAddErr: errors.New("batch add failed"),
			wantDLQ:     0, wantBatchAdds: 0,
		},
		{
			name:    "happy path",
			wantDLQ: 0, wantBatchAdds: 1,
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			tc := tc
			// --- setup fakes ---
			conns := mocks.FakeConns()
			psc := conns.Pubsub.(*mocks.FakePubsubClient)
			psc.ReceiveError = tc.recvErr

			// create subscription
			topic := psc.Topic("topic-" + tc.name)
			_, err := psc.CreateSubscription(ctx, "sub-"+tc.name, connect.SubscriptionConfig{Topic: topic})
			if err != nil {
				t.Fatal(err)
			}

			// publish message if connector & receive ok
			if tc.connErr == nil && tc.recvErr == nil {
				msg := createTestMessage(t, "test log message")
				topic.Publish(ctx, msg)
			}

			// fake batcher
			added := 0
			fakeBatch, _ := mocks.FakeBatch(ctx)
			fakeBatch.(*mocks.FakeBatcher).AddFn = func(row interface{}) error {
				if tc.batchAddErr != nil {
					return tc.batchAddErr
				}
				added++
				return nil
			}

			// collect DLQ calls
			dlq := 0

			// --- build deps ---
			deps := HandlerDeps{
				Connector: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
					return conns, tc.connErr
				},
				SendToDLQ: func(ctx context.Context, client connect.PsClient, m *pubsub.Message, reason string) error {
					if tc.dlqErr != nil {
						return tc.dlqErr
					}
					dlq++
					return nil
				},
				UnmarshalGatewayLogs: func(raw []byte) (*gatewayv1.GatewayLogs, error) {
					if tc.unmarshalErr != nil {
						return nil, tc.unmarshalErr
					}
					return &gatewayv1.GatewayLogs{
						Message:     raw,
						MessageTime: timestamppb.New(time.Now()),
					}, nil
				},
				GetBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return fakeBatch, tc.batchErr
				},
				ToBQ: func(orgID string, sgwID string, tz string, topic string, pubsubID string, pubsubTS time.Time, messageTime time.Time, softwareGatewayDeviceID string, logMessage string, rawMessage []byte) schemas.GatewayLogMessage {
					return schemas.GatewayLogMessage{}
				},
				ParseAttributes: func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
					if tc.attrErr != nil {
						return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, tc.attrErr
					}
					return pubsubdata.CommonAttributes{
							OrganizationIdentifier: "test-org",
							Topic:                  "test-topic",
							DeviceType:             "test-device",
						}, pubsubdata.HeaderDetails{
							GatewayDeviceID: "test-gateway",
							GatewayTimezone: "UTC",
						}, nil
				},
				DecompressBytes: func(compressedData []byte) ([]byte, error) {
					if tc.decompressErr != nil {
						return nil, tc.decompressErr
					}
					return []byte("test log message"), nil
				},
			}

			// --- invoke handler ---
			h := HandlerWithDeps(deps)
			h(ctx, "sub-"+tc.name)

			// --- asserts ---
			assert.Equal(t, tc.wantDLQ, dlq, "DLQ calls should match expected")
			assert.Equal(t, tc.wantBatchAdds, added, "batch.Add calls should match expected")
		})
	}
}

func TestHandler(t *testing.T) {
	// Setup test data
	msg := createTestMessage(t, "test log message")

	// Setup mocks
	mockConn := &mockConnector{}
	mockPs := &mockPubsubClient{}
	mockSub := &mockSubscription{}

	// Setup expectations
	mockConn.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{
		Pubsub: mockPs,
	}, nil)

	mockPs.On("Subscription", mock.Anything).Return(mockSub)

	mockSub.On("Receive", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		f := args.Get(1).(func(context.Context, *pubsub.Message))
		f(context.Background(), msg)
	}).Return(nil)

	// Create handler with mocks
	handler := HandlerWithDeps(HandlerDeps{
		Connector: mockConn.GetConnections,
		SendToDLQ: func(ctx context.Context, client connect.PsClient, msg *pubsub.Message, reason string) error {
			return nil
		},
		UnmarshalGatewayLogs: etlShared.UnmarshalGatewayLogs,
		GetBatch:             func(ctx context.Context) (bqbatch.Batcher, error) { return mocks.FakeBatch(ctx) },
		ToBQ: func(orgID string, sgwID string, tz string, topic string, pubsubID string, pubsubTS time.Time, messageTime time.Time, softwareGatewayDeviceID string, logMessage string, rawMessage []byte) schemas.GatewayLogMessage {
			return schemas.GatewayLogMessage{}
		},
		ParseAttributes: func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
			return pubsubdata.CommonAttributes{
					OrganizationIdentifier: "test-org",
					Topic:                  "test-topic",
					DeviceType:             "test-device",
				}, pubsubdata.HeaderDetails{
					GatewayDeviceID: "test-gateway",
					GatewayTimezone: "UTC",
				}, nil
		},
		DecompressBytes: helper.DecompressBytes,
	})

	// Execute handler
	handler(context.Background(), "test-subscription")

	// Verify expectations
	mockConn.AssertExpectations(t)
	mockPs.AssertExpectations(t)
	mockSub.AssertExpectations(t)
}

func TestHandlerErrorCases(t *testing.T) {
	// Create a test message with invalid data
	msg := &pubsub.Message{
		ID:          "test-msg-id",
		Data:        []byte("invalid data"),
		PublishTime: time.Now(),
	}

	// Setup mocks
	mockConn := &mockConnector{}
	mockPs := &mockPubsubClient{}
	mockSub := &mockSubscription{}

	// Setup expectations
	mockConn.On("GetConnections", mock.Anything, mock.Anything).Return(&connect.Connections{
		Pubsub: mockPs,
	}, nil)

	mockPs.On("Subscription", mock.Anything).Return(mockSub)

	mockSub.On("Receive", mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
		f := args.Get(1).(func(context.Context, *pubsub.Message))
		f(context.Background(), msg)
	}).Return(nil)

	// Create handler with mocks
	handler := HandlerWithDeps(HandlerDeps{
		Connector: mockConn.GetConnections,
		SendToDLQ: func(ctx context.Context, client connect.PsClient, msg *pubsub.Message, reason string) error {
			return nil
		},
		UnmarshalGatewayLogs: etlShared.UnmarshalGatewayLogs,
		GetBatch:             func(ctx context.Context) (bqbatch.Batcher, error) { return nil, nil },
		ToBQ: func(orgID string, sgwID string, tz string, topic string, pubsubID string, pubsubTS time.Time, messageTime time.Time, softwareGatewayDeviceID string, logMessage string, rawMessage []byte) schemas.GatewayLogMessage {
			return schemas.GatewayLogMessage{}
		},
		ParseAttributes: func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
			return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, nil
		},
		DecompressBytes: helper.DecompressBytes,
	})

	// Execute handler
	handler(context.Background(), "test-subscription")

	// Verify expectations
	mockConn.AssertExpectations(t)
	mockPs.AssertExpectations(t)
	mockSub.AssertExpectations(t)
}
