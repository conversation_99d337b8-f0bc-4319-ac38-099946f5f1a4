#!/usr/bin/env bash
set -euo pipefail

KC_HOME=/opt/keycloak
APP_DIR=/app
REALM_NAME=${REALM_NAME:-onramp-dev}
REALM_FILE=$APP_DIR/realm-export.json
TMP_EXPORT=$APP_DIR/realm-export.tmp.json

# 1) Import on cold start
if [[ -f "$REALM_FILE" ]]; then
  # echo "Importing realm from $REALM_FILE"
  "$KC_HOME/bin/kc.sh" import --file "$REALM_FILE" --override \
    || true # echo "Import failed (continuing)"
fi

# 2) Define cleanup (will run on SIGTERM)
cleanup() {
  # echo "SIGTERM received; stopping Keycloak..."
  kill -- -"$KC_PID"   || true
  wait "$KC_PID"       || true

  # echo "Raw export of realm '$REALM_NAME' to $TMP_EXPORT"
  "$KC_HOME/bin/kc.sh" export \
    --realm     "$REALM_NAME" \
    --file      "$TMP_EXPORT" \
    --users     same_file \
    --optimized

  # echo "Canonicalizing JSON"
  jq -S 'walk(if type=="array" then sort else . end)' "$TMP_EXPORT" \
     > "${TMP_EXPORT}.canon"
  mv "${TMP_EXPORT}.canon" "$TMP_EXPORT"

  # echo "Comparing to existing $REALM_FILE"
  if cmp -s "$TMP_EXPORT" "$REALM_FILE"; then
    # echo "no real changes"
    :
  else
    mv "$TMP_EXPORT" "$REALM_FILE"
    # echo "realm-export.json updated"
  fi
}
trap cleanup SIGTERM SIGINT

# echo "Starting Keycloak in dev mode…"
setsid "$KC_HOME/bin/kc.sh" start-dev &
KC_PID=$!

# now *this* shell stays alive, waiting for Keycloak or a SIGTERM
wait "$KC_PID"
