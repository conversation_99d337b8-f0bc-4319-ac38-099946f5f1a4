<#
.SYNOPSIS
  Start your development microservices.

.PARAMETER Group
  Which group of services to run. Valid values: core, ui, all.

.PARAMETER DumpKeycloak
  Dump Keycloak DB on shutdown (only valid with ui or all).

.EXAMPLE
  .\dev.ps1 core

.EXAMPLE
  .\dev.ps1 ui -DumpKeycloak
#>

# ====================
# If the script is run with the `-DumpKeycloak` switch, it will dump the
# Keycloak database to a file.  It is not enabled by default because it takes
# over 2 minutes to run.  It is only run when the script is shutting down,
# and not on the restart/reload cycles.
# ====================
[CmdletBinding()]
param(
  [Parameter(Position=0)]
  [ValidateSet('core','ui','all')]
  [string]$Group,

  [switch]$DumpKeycloak
)
if (-not $Group) {
  Get-Help $MyInvocation.MyCommand.Path -Full
  exit 1
}

if ($DumpKeycloak -and $Group -eq 'core') {
  Write-Error "–DumpKeycloak can only be used with 'ui' or 'all'" -ErrorAction Stop
}

# ====================
# Enable UTF-8 output so Unicode renders correctly
# ====================
chcp 65001 | Out-Null
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::InputEncoding  = [System.Text.Encoding]::UTF8


# ====================
# Setup Environment Files
# ====================
# Get the directory where the script resides.
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Definition

# Ensure that the required environment files exist; if not, create them.
'coordinator','broker','etl','onramp','testing' |
  ForEach-Object {
    $envFile = Join-Path $ScriptDir "..\infra\.env.$_.local"
    if (-Not (Test-Path $envFile)) {
      New-Item -Path $envFile -ItemType "File" -Force | Out-Null
    }
  }
if (-Not (Test-Path (Join-Path $ScriptDir '..\infra\.env.local'))) {
  New-Item -Path (Join-Path $ScriptDir '..\infra\.env.local') -ItemType "File" -Force | Out-Null
}

if (-Not (Test-Path (Join-Path $ScriptDir '..\creds.json'))) {
  New-Item -Path (Join-Path $ScriptDir '..\creds.json') -ItemType "File" -Force | Out-Null
}

# ====================
# Define Paths & Watch Settings
# ====================
# Compute the path to the infra directory relative to the script.
$ComposeFile = Join-Path $ScriptDir "..\infra\docker-compose.dev.yml"

# Compute the repository root (assumed to be the parent directory of scripts).
$RepoRoot = (Resolve-Path (Join-Path $ScriptDir '..') -ErrorAction Stop).Path

# Global flag that indicates a file change triggering a container restart.
$global:TriggerRestart = $false


# ====================
# Classify microservices by lifecycle
# ====================
$AllMicroservices = docker compose -f $ComposeFile config --services
$MicroservicesToRemoveOnRebuild = @( 'pubsub', 'redis', 'postgres', 'bigquery' )
$LongRunningMicroservices = @( 'keycloak', 'selenium' )
#$SimpleRestartMicroservices = $AllMicroservices | Where-Object { ($MicroservicesToRemoveOnRebuild + $LongRunningMicroservices) -notcontains $_ }
$NotLongRunningMicroservices = $AllMicroservices | Where-Object { $LongRunningMicroservices -notcontains $_ }

$ServiceGroups = @{
  core = @(
    'coordinator','broker','etl','pubsub','bigquery','redis','postgres','testing'
  )
  ui = @(
    'coordinator','pubsub','bigquery','redis','postgres',
    'onramp','selenium','cucumber','keycloak'
  )
  all = $AllMicroservices
}
$SelectedServices = $ServiceGroups[$Group]


# ====================
# File System Watcher Setup
# ====================

# Subscriptions
$Subscriptions = @()

# File extensions to watch.
$WatchExtensions = @("*.go", "*.sql", "*.ts", "*.js", "*.env", "*.json", "*.yaml", "*.yml", "Dockerfile", "*.feature")

# Initialize flags
$global:CanTrigger = $true

# -----------------------------------------------------------------------------
# 1) Throttle timer: one‐shot 30 000 ms, auto-reset = $false
# -----------------------------------------------------------------------------
$global:throttleTimer = New-Object Timers.Timer 30000
$global:throttleTimer.AutoReset = $false

# When it elapses, re-enable triggering
$Subscriptions += Register-ObjectEvent $global:throttleTimer Elapsed -Action {
  $global:CanTrigger = $true
}

# -----------------------------------------------------------------------------
# 2) FileSystemWatcher (already pointing at a valid $RepoRoot)
# -----------------------------------------------------------------------------
$watcher = New-Object IO.FileSystemWatcher($RepoRoot)
$watcher.IncludeSubdirectories = $true
$watcher.NotifyFilter = [IO.NotifyFilters]"LastWrite,FileName,DirectoryName"
$watcher.InternalBufferSize = 64kb        # enlarge buffer against overflow
$watcher.Filters.Clear()
$WatchExtensions | ForEach-Object { $watcher.Filters.Add($_) }

# On any filesystem event, trigger immediately (if allowed) then start throttle
$onChange = {
  param($s,$e)

  # Don't do anything if the change is in 'package-lock.json', 'package.json', or 'devcontainer.json'
  $leaf = Split-Path $e.FullPath -Leaf

  # list of exact names to ignore
  $ignore = @(
    'package-lock.json',
    'package.json',
    'devcontainer.json',
    'creds.json'
  )

  # Let Angular manage it's own restarts, so ignore any paths that contain
  # '/microservices/onramp/.ui/'
  if ( $e.FullPath -match '\\microservices\\onramp\\.ui\\' ) {
    # Write-Host "Ignoring change: $($e.FullPath)"
    return
  }

  if ( $ignore -contains $leaf ) {
    # Write-Host "Ignoring change: $($e.FullPath)"
    return
  }

  # Say where the change occurred
  # Write-Host "File change: $($e.FullPath)"
  if ($global:CanTrigger) {
    $global:TriggerRestart = $true
    $global:CanTrigger     = $false
    $global:throttleTimer.Stop()
    $global:throttleTimer.Start()
  }
}

'Changed','Created','Renamed','Deleted' | ForEach-Object {
  $Subscriptions += Register-ObjectEvent $watcher $_ -Action $onChange
}

# Watcher‐error handler (in case buffer overflows)
$Subscriptions += Register-ObjectEvent $watcher Error -Action {
  Write-Warning "FileSystemWatcher error, restarting watcher…"
  $watcher.EnableRaisingEvents = $false
  Start-Sleep -Milliseconds 500
  $watcher.EnableRaisingEvents = $true
}

$watcher.EnableRaisingEvents = $true


# ====================
# Helper function to only remove anonymous (hex-named) volumes
# NOTE: This is a workaround for an observed behavior.  Unfortnately,
# `docker volume prune` is removing named volumes as well, which is bad,
# because the dev container may or may not be running at the time.
# This script, then, will look for volumes that have the default name
# pattern (hexadecimal, 64 characters) and remove them.
# ====================
function Remove-UnnamedVolumes {
  # Gather all dangling volumes whose names are 64-hex chars
  $ids = docker volume ls -f dangling=true -q |
         Where-Object { $_ -match '^[0-9a-f]{64}$' }

  if ($ids) {
    # Remove them all in one command
    docker volume rm $ids
  }
}


# ====================
# Composer-related variables
# ====================

# Keeps a reference to the `docker compose` process
$global:ComposeJob = $null

# At top‐level in your script, keep a place to cache last Dockerfile times:
$global:LastDockerfileTimes = @{}

# Track whether or not this is the first run.  On the first run, we will start
# the long-running services (keycloak, selenium), but not on subsequent runs.
$script:FirstRun = $true


# ====================
# Container Start
# ====================
function Start-ComposeUp {
  param($ServicesToStart)

  # 1) Detect Dockerfile changes
  $needRebuild = $false

  $services = @('broker','etl','coordinator','onramp','testing')

  foreach ($svc in $services) {
    $df = Join-Path $RepoRoot "microservices\$svc\Dockerfile"
    if (-Not (Test-Path $df)) { continue }

    $current = (Get-Item $df).LastWriteTimeUtc
    if (-Not $global:LastDockerfileTimes.ContainsKey($svc) -or
        $current -gt $global:LastDockerfileTimes[$svc]) {
      # Dockerfile is new or changed since last run
      $needRebuild = $true
      $global:LastDockerfileTimes[$svc] = $current
    }
  }

  # 2) If anything changed, rebuild those services
  if ($needRebuild) {
    Write-Host "Dockerfile change detected; rebuilding images..."

    $env:CURRENT_TIMESTAMP = ""
    docker compose -f $ComposeFile build
  }

  # 3) Now bring up services

  # If this is the first run, then include the long-running services.
  if ($script:FirstRun) {
    $script:FirstRun = $false
    $services = $ServicesToStart
  }
  else {
    $services = $ServicesToStart | Where-Object { $LongRunningMicroservices -notcontains $_ }
  }
  # Log the services we are starting.
  Write-Host "`nStarting services: $($services -join ', ')"

  $global:ComposeJob = Start-Job -ScriptBlock {
    # NOTE: (To my future self)
    # This script block runs in a different process and cannot see
    # the variables in the parent process.  We must therefore pass
    # everything we need as arguments.
    param($composeFile, $services)

    # Fix UTF-8 output in the child process.
    chcp 65001 | Out-Null
    [Console]::OutputEncoding = [System.Text.Encoding]::UTF8
    [Console]::InputEncoding  = [System.Text.Encoding]::UTF8

    # Compute the current timestamp in milliseconds since the Unix epoch.
    $CurrentTimestamp = [int64]((Get-Date).ToUniversalTime() - [datetime]'1970-01-01').TotalMilliseconds

    # Set the environment variable CURRENT_TIMESTAMP for this process.
    $env:CURRENT_TIMESTAMP = $CurrentTimestamp

    # Actually start the services.
    docker compose --ansi=always -f $composeFile up -d $services

    # Tail the logs for all services (including stderr).
    docker compose --ansi=always -f $composeFile logs -f --tail=0

  } -ArgumentList $ComposeFile, $services
}


# ====================
# Container Stop
# ====================
function Stop-ComposeUp {
  param($ServicesToStop)

  if ($global:ComposeJob -ne $null) {
    Write-Host "`nStopping containers..."
    $toStop = $ServicesToStop | Where-Object { $NotLongRunningMicroservices -contains $_ }
    $toRm = $toStop | Where-Object { $MicroservicesToRemoveOnRebuild -contains $_ }

    # Set the environment variable CURRENT_TIMESTAMP for this process to avoid
    # error messages.  The value is not important for this operation.
    $env:CURRENT_TIMESTAMP = "foo"

    docker compose -f $ComposeFile stop $toStop
    docker compose -f $ComposeFile rm -sfv $toRm
    if ($global:ComposeJob.State -eq 'Running') {
      Stop-Job $global:ComposeJob
    }
    Remove-Job $global:ComposeJob
    $global:ComposeJob = $null
    Write-Host "Containers and Compose Job stopped."
  }
  else {
    Write-Host "No Compose Job to stop."
  }
}


# ====================
# Add color output to logs by message severity.
# ====================
function Format-LogLine {
  param([string]$line)

  # Split off the prefix (up through the first '|')
  $idx = $line.IndexOf('|')
  if ($idx -lt 0) { return $line }
  $prefix   = $line.Substring(0, $idx+1)
  $rest     = $line.Substring($idx+1)

  # Regex to find "message":"...content..." allowing \" escapes
  $pattern = '"message"\s*:\s*"((?:\\.|[^"\\])*)"'

  # Determine severity so we know which color to pick
  if ($rest -notmatch '"severity"\s*:\s*"(?<sev>[^"]+)"') {
    return $line
  }
  $sev = $Matches['sev'].ToLower()

  # Pick ANSI color
  $esc   = [char]27
  $reset = "$esc[0m"
  switch ($sev) {
    'debug'   { $color = "$esc[44;97m" }  # blue bg, white text
    'info'    { $color = "$esc[48;5;22;97m" }  # 256‑color dark green bg (22), white text (97)
    'warn'    { $color = "$esc[43;30m" }  # yellow bg, black text
    'warning' { $color = "$esc[43;30m" }  # yellow bg, black text
    'error'   { $color = "$esc[41;97m" }  # red bg, white text
    default   { $color = "" }
  }

  # Define a MatchEvaluator that wraps group 1 (the raw JSON content) in color codes
  $evaluator = {
    param($m)
    # $m.Groups[1].Value is the raw content, e.g. He said \"hello\" to me
    $inner = $m.Groups[1].Value
    # We reinsert it literally, preserving escapes, but wrapped in ANSI
    return '"message":"' + $color + $inner + $reset + '"'
  }

  # Do the replacement on $rest
  try {
    $newRest = [regex]::Replace($rest, $pattern, $evaluator)
    return $prefix + " " + $newRest
  }
  catch {
    # On any failure, just return the original
    return $line
  }
}


# ====================
# Cleanup Function
# ====================
function Cleanup {
  # 1) Stop the watcher and timer first
  $watcher.EnableRaisingEvents = $false
  $global:throttleTimer.Stop()

  # 2) Unregister every subscription we captured
  foreach ($sub in $Subscriptions) {
    try {
      Unregister-Event -SubscriptionId $sub.Id -ErrorAction SilentlyContinue
    } catch { }
  }
  $Subscriptions = @()      # clear the list

  # 3) Dispose the objects so they'll free resources
  $watcher.Dispose()
  $global:throttleTimer.Dispose()

  # 4) Clean up Docker containers and volumes
  Write-Host "`nCleaning up old volumes..."
  Remove-UnnamedVolumes
  Stop-ComposeUp -ServicesToStop $SelectedServices

  # 5) Clean up the long-running services.
  # If we are dumping Keycloak, then give it extra time to export.
  # This is enabled by passing the `-DumpKeycloak` switch to the script.
  if ($DumpKeycloak) {
    Write-Host "`nWaiting up to 180s for Keycloak to export..."
    docker compose -f $ComposeFile stop --timeout 180
  }
  else {
    docker compose -f $ComposeFile stop --timeout 5
  }
}


# ====================
# Register CTRL+C Handler
# ====================
$Subscriptions += Register-EngineEvent -SourceIdentifier ConsoleCancelEvent -Action {
  Cleanup
  exit 130
}


# ====================
# Main Loop
# ====================

try {
  while ($true) {
    # Start services...
    Start-ComposeUp -ServicesToStart $SelectedServices

    # Capture when we started, so we can filter the first 15 seconds of certain lines
    $startTime = Get-Date

    # Tail, filter, and process docker compose logs
    while (-not $global:TriggerRestart) {
      Start-Sleep -Milliseconds 100

      if ($global:ComposeJob -ne $null) {
        $output = Receive-Job -Job $global:ComposeJob -ErrorAction SilentlyContinue
        if ($output) {
          # Calculate elapsed seconds
          $elapsed = ((Get-Date) - $startTime).TotalSeconds

          foreach ($line in $output) {
            # 1) Strip ANSI sequences
            $esc         = [char]27
            $ansiPattern = "$esc\[[0-9;]*[@-~]"
            $clean       = [regex]::Replace($line, $ansiPattern, "")

            # 2) Always skip this Keycloak cookie warning.
            #    It spams the logs and cannot be disabled.
            #    It is complaining about using http instead of https.
            if ($clean -match 'org\.keycloak\.cookie\.DefaultCookieProvider') {
              continue
            }

            # 3) Within the first 15s, also skip service-startup noise
            if ($elapsed -lt 15 -and $clean -match '^(redis|cloudsql|bigquery|pubsub)') {
              continue
            }

            # 4) Otherwise, emit the formatted line
            Write-Output (Format-LogLine $line)
          }
        }
      }
    }

    Write-Output "Change detected. Restarting containers..."
    Stop-ComposeUp -ServicesToStop $SelectedServices
    $global:TriggerRestart = $false
  }
}
finally {
  Cleanup
}
