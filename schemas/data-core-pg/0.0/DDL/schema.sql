-- Synapse.Config definition

CREATE TABLE {{Config}} (
  Key varchar(128) NOT NULL,
  Value text NOT NULL,
  Description varchar(255) NOT NULL,
  PRIMARY KEY (Key)
);

-- Synapse.DeviceCommunicationConfig definition

CREATE TABLE {{DeviceCommunicationConfig}} (
  SoftwareGatewayId int NOT NULL,
  DeviceIdentifier varchar(128) NOT NULL,
  Latitude varchar(20) NOT NULL,
  Longitude varchar(20) NOT NULL,
  IPAddress varchar(20) NOT NULL,
  Port varchar(6) NOT NULL,
  FlushConnectionMs varchar(5) NOT NULL,
  EnableRealtime varchar(5) NOT NULL,
  IsEnabled smallint NOT NULL DEFAULT 1,
  Description varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (SoftwareGatewayId, DeviceIdentifier),
  CONSTRAINT {{DeviceCommConfig_SoftwareGatewayID_DeviceIdentifier_IPAddress}}
    UNIQUE (SoftwareGatewayId, DeviceIdentifier, IPAddress)
);

-- Synapse.Organization definition

CREATE TABLE {{Organization}} (
  Id int NOT NULL,
  OrganizationIdentifier varchar(80) NOT NULL,
  APIKey varchar(255) NOT NULL,
  Description varchar(255) NOT NULL,
  CreatedAt timestamp NOT NULL DEFAULT now(),
  UpdatedAt timestamp NOT NULL DEFAULT now(),
  DeletedAt timestamp,
  IsDeleted boolean NOT NULL DEFAULT false,
  PRIMARY KEY (Id)
);

-- Synapse.Permission definition

CREATE TABLE {{Permission}} (
  Id int NOT NULL,
  Definition varchar(255) NOT NULL,
  Description varchar(255) NOT NULL,
  PRIMARY KEY (Id)
);

-- Synapse.Role definition

CREATE TABLE {{Role}} (
  Id int NOT NULL,
  Description varchar(255) NOT NULL,
  PRIMARY KEY (Id)
);

-- Synapse.SoftwareGatewayInstructionList definition

CREATE TABLE {{SoftwareGatewayInstructionList}} (
  Instruction varchar(128) NOT NULL,
  PRIMARY KEY (Instruction)
);

-- Synapse.SoftwareGatewayInstructionStatus definition

CREATE TABLE {{SoftwareGatewayInstructionStatus}} (
  Status varchar(128) NOT NULL,
  PRIMARY KEY (Status)
);

-- Synapse.SoftwareGateway definition

CREATE TABLE {{SoftwareGateway}} (
  Id int NOT NULL,
  OrganizationId int NOT NULL,
  SoftwareGatewayIdentifier varchar(80) NOT NULL,
  APIKey varchar(255) NOT NULL,
  Token varchar(80),
  DateLastCheckedInUTC timestamp,
  PushConfigOnNextCheck boolean NOT NULL DEFAULT false,
  IsEnabled boolean NOT NULL DEFAULT false,
  Description varchar(255) NOT NULL,
  PRIMARY KEY (Id),
  CONSTRAINT {{SoftwareGateway_SoftwareGatewayIdentifier}} UNIQUE (SoftwareGatewayIdentifier),
  CONSTRAINT {{SoftwareGateway_OrganizationId}} FOREIGN KEY (OrganizationId)
    REFERENCES {{Organization}} (Id)
);

-- Synapse.SoftwareGatewayConfig definition

CREATE TABLE {{SoftwareGatewayConfig}} (
  SoftwareGatewayId int NOT NULL,
  MaximumDevices int NOT NULL,
  Config text NOT NULL,
  PRIMARY KEY (SoftwareGatewayId),
  CONSTRAINT {{SoftwareGatewayConfig_SoftwareGateway_Id}} FOREIGN KEY (SoftwareGatewayId)
    REFERENCES {{SoftwareGateway}} (Id)
);

-- Synapse.SoftwareGatewayStatistics definition

CREATE TABLE {{SoftwareGatewayStatistics}} (
  SoftwareGatewayId int NOT NULL,
  DateUploadedUTC timestamp NOT NULL,
  PerfStatsBase64Zip text NOT NULL,
  PRIMARY KEY (SoftwareGatewayId),
  CONSTRAINT {{SoftwareGatewayStatistics_SoftwareGatewayId}} FOREIGN KEY (SoftwareGatewayId)
    REFERENCES {{SoftwareGateway}} (Id)
);

-- Synapse.User definition

CREATE TABLE {{User}} (
  Id int NOT NULL,
  OrganizationId int NOT NULL,
  RoleId int NOT NULL,
  UserName varchar(255) NOT NULL,
  Password varchar(80) NOT NULL,
  FirstName varchar(45),
  LastName varchar(45),
  Email varchar(255),
  Mobile varchar(45),
  NotificationSmsEnabled boolean NOT NULL DEFAULT false,
  IANATimezone varchar(45) NOT NULL DEFAULT 'America/Chicago',
  Description varchar(255),
  FailedLoginAttempts int NOT NULL DEFAULT 0,
  IsEnabled smallint NOT NULL DEFAULT 0,
  LastLoginUTC timestamp,
  TokenDurationHours int NOT NULL DEFAULT 1,
  WebEnabled smallint NOT NULL DEFAULT 0,
  WebTokenDurationSeconds smallint NOT NULL DEFAULT 0,
  PRIMARY KEY (Id),
  CONSTRAINT {{UserName_UNIQUE}} UNIQUE (UserName),
  CONSTRAINT {{User_Organization}} FOREIGN KEY (OrganizationId)
    REFERENCES {{Organization}} (Id),
  CONSTRAINT {{User_RoleId}} FOREIGN KEY (RoleId)
    REFERENCES {{Role}} (Id)
);

-- Synapse.UserSoftwareGateway definition

CREATE TABLE {{UserSoftwareGateway}} (
  UserId int NOT NULL,
  SoftwareGatewayId int NOT NULL,
  PermissionId int NOT NULL,
  PRIMARY KEY (UserId, SoftwareGatewayId),
  CONSTRAINT {{UserSoftwareGateway_PermissionId}} FOREIGN KEY (PermissionId)
    REFERENCES {{Permission}} (Id),
  CONSTRAINT {{UserSoftwareGateway_SoftwareGatewayId}} FOREIGN KEY (SoftwareGatewayId)
    REFERENCES {{SoftwareGateway}} (Id),
  CONSTRAINT {{UserSoftwareGateway_UserId}} FOREIGN KEY (UserId)
    REFERENCES {{User}} (Id)
);

-- Synapse.UserToken definition

CREATE TABLE {{UserToken}} (
  UserId int NOT NULL,
  JWTToken varchar(4096) NOT NULL,
  JWTTokenSha256 varchar(80) NOT NULL,
  CreatedUTC timestamp NOT NULL,
  ExpirationUTC timestamp NOT NULL,
  CONSTRAINT {{UserToken_UserId}} FOREIGN KEY (UserId)
    REFERENCES {{User}} (Id)
);

-- Synapse.Device definition

CREATE TABLE {{Device}} (
  Id int NOT NULL,
  DeviceIdentifier varchar(128) NOT NULL,
  SoftwareGatewayId int NOT NULL,
  Description varchar(255) NOT NULL,
  Type varchar(255) NOT NULL,
  PRIMARY KEY (Id),
  CONSTRAINT {{DeviceIdentifier_UNIQUE}} UNIQUE (DeviceIdentifier),
  CONSTRAINT {{Device_SoftwareGateway}} FOREIGN KEY (SoftwareGatewayId)
    REFERENCES {{SoftwareGateway}} (Id)
);

CREATE TABLE {{DeviceMonitorName}} (
  DeviceIdentifier varchar(128) NOT NULL,
  MonitorId int,
  MonitorName varchar(128),
  PRIMARY KEY (DeviceIdentifier),
  CONSTRAINT {{DeviceMonitorName_DeviceIdentifier}} FOREIGN KEY (DeviceIdentifier)
    REFERENCES {{Device}} (DeviceIdentifier)
);

CREATE TABLE {{DeviceRMSEngine}} (
  DeviceIdentifier varchar(128) NOT NULL,
  EngineVersion int,
  EngineRevision int,
  PRIMARY KEY (DeviceIdentifier),
  CONSTRAINT {{DeviceRMSEngine_DeviceIdentifier}} FOREIGN KEY (DeviceIdentifier)
    REFERENCES {{Device}} (DeviceIdentifier)
);

-- Synapse.DeviceFault definition
-- TODO: Remove once dependency from /data/device is removed

CREATE TABLE {{DeviceFault}} (
  DeviceIdentifier varchar(128) NOT NULL,
  IsFaulted BOOLEAN,
  Fault varchar(255),
  FaultStatus varchar(255),
  ChannelGreenStatus BOOLEAN[],
  ChannelYellowStatus BOOLEAN[],
  ChannelRedStatus BOOLEAN[],
  MonitorTime timestamp,
  Temperature int,
  VoltagesGreen INT[],
  VoltagesYellow INT[],
  VoltagesRed INT[],
  DeviceModel varchar(255),
  PRIMARY KEY (DeviceIdentifier),
  CONSTRAINT {{DeviceFault_DeviceIdentifier}} FOREIGN KEY (DeviceIdentifier)
    REFERENCES {{Device}} (DeviceIdentifier)
);

-- Synapse.DeviceLog definition
-- TODO: Remove once dependency from /data/device is removed

CREATE TABLE {{DeviceLog}} (
  DeviceIdentifier varchar(128) NOT NULL,
  DateUploadedUTC timestamp NOT NULL,
  UserUploaded smallint NOT NULL,
  LogUUID varchar(255) NOT NULL,
  PRIMARY KEY (DeviceIdentifier),
  CONSTRAINT {{DeviceLog_DeviceIdentifier}} FOREIGN KEY (DeviceIdentifier)
    REFERENCES {{Device}} (DeviceIdentifier)
);

-- Synapse.DeviceSoftwareUpdate definition

CREATE TABLE {{DeviceSoftwareUpdate}} (
  DeviceId int NOT NULL,
  S3Location varchar(1024) NOT NULL,
  IsAvailable smallint NOT NULL,
  DownloadLocation varchar(1024),
  ExpiresUTC timestamp,
  PRIMARY KEY (DeviceId),
  CONSTRAINT {{devicefotwareupdate_deviceid}} FOREIGN KEY (DeviceId)
    REFERENCES {{Device}} (Id)
);

-- Synapse.DeviceState definition
-- TODO: Remove as this will be implemented in BQ and Redis

CREATE TABLE {{DeviceState}} (
  DeviceId int NOT NULL,
  State varchar(45) NOT NULL,
  HeartbeatReceivedUTC timestamp NOT NULL,
  PRIMARY KEY (DeviceId),
  CONSTRAINT {{devicestate_deviceid}} FOREIGN KEY (DeviceId)
    REFERENCES {{Device}} (Id)
);

-- Synapse.SoftwareGatewayInstruction definition

CREATE TABLE {{SoftwareGatewayInstruction}} (
  UserId int NOT NULL,
  DeviceId int NOT NULL,
  Instruction varchar(128) NOT NULL,
  DateQueuedUTC timestamp NOT NULL,
  DateReceivedUTC timestamp,
  Status varchar(128) NOT NULL,
  CONSTRAINT {{SoftwareGatewayInstruction_DeviceId}} FOREIGN KEY (DeviceId)
    REFERENCES {{Device}} (Id),
  CONSTRAINT {{SoftwareGatewayInstruction_Instruction}} FOREIGN KEY (Instruction)
    REFERENCES {{SoftwareGatewayInstructionList}} (Instruction),
  CONSTRAINT {{SoftwareGatewayInstruction_Status}} FOREIGN KEY (Status)
    REFERENCES {{SoftwareGatewayInstructionStatus}} (Status),
  CONSTRAINT {{SoftwareGatewayInstruction_UserId}} FOREIGN KEY (UserId)
    REFERENCES {{User}} (Id)
);

-- Synapse.UserDevice definition

CREATE TABLE {{UserDevice}} (
  UserId int NOT NULL,
  DeviceId int NOT NULL,
  RoleId int NOT NULL,
  PRIMARY KEY (UserId, DeviceId),
  CONSTRAINT {{UserDevice_DeviceId}} FOREIGN KEY (DeviceId)
    REFERENCES {{Device}} (Id),
  CONSTRAINT {{UserDevice_RoleId}} FOREIGN KEY (RoleId)
    REFERENCES {{Role}} (Id),
  CONSTRAINT {{UserDevice_UserId}} FOREIGN KEY (UserId)
    REFERENCES {{User}} (Id)
);
