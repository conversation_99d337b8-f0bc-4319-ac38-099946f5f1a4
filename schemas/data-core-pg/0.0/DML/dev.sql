-- Data for the gateway developers. Do not edit unless required to!!!
INSERT INTO {{Organization}} (Id,OrganizationIdentifier,APIKey,Description) VALUES
  (1,'DEV5AC4410CB8F3C8E29CE9210E5913B7B3B872B2BC','US4ZHOzPZ7a2b5ezFldb86jZ1UdUkyIg41hCcqea','Cornelius''s Organization'),
  (50,'TAMDEVGATEWAYORG','US4ZHOzPZ7a2b5ezFldb86jZ1UdUkyIg41hCcqea','Tam''s Organization'),
  (51,'DUCDEVGATEWAYORG','US4ZHOzPZ7a2b5ezFldb86jZ1UdUkyIg41hCcqea','Duc''s Organization'),
  (52,'MINHDEVGATEWAYORG','US4ZHOzPZ7a2b5ezFldb86jZ1UdUkyIg41hCcqea','Minh''s Organization');

INSERT INTO {{SoftwareGateway}} (Id,OrganizationId,SoftwareGatewayIdentifier,API<PERSON><PERSON>,<PERSON><PERSON>,DateLastCheckedInUTC,PushConfigOnNextCheck,IsEnabled,Description) VALUES
  (1,1,'d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f','qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd',NULL,'2025-04-07 15:24:08',true,true,'Cornelius - Gateway'),
  (10,50,'TAMDEVGATEWAY','qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd',NULL,'2025-05-27 15:25:49',false,true,'Tam - Dev Gateway'),
  (11,51,'DUCDEVGATEWAY','qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd',NULL,'2025-06-02 04:13:12',false,true,'Duc - Dev Gateway'),
  (12,52,'MINHDEVGATEWAY','qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd',NULL,'2025-04-15 10:46:25',false,true,'Minh - Dev Gateway');

INSERT INTO {{SoftwareGatewayConfig}} (SoftwareGatewayId,MaximumDevices,Config) VALUES
  (1,3,'{"log_level": "error", "log_filename": "log/gateway-app.log", "log_max_backups": 10, "application_version": "deprecated", "log_max_age_in_days": 30, "log_compress_backups": true, "log_file_max_size_mb": 10, "rest_api_device_endpoint": "https://api.dev.synapse-its.app/ingest/v3/state", "send_gateway_logs_to_cloud": false, "edi_device_persist_connection": true, "edi_device_processing_retries": 5, "record_http_requests_to_folder": false, "device_state_send_frequency_seconds": 60, "config_change_check_frequency_seconds": 30, "send_gateway_performance_stats_to_cloud": true, "software_update_check_frequency_seconds": 60, "channel_state_send_frequency_milliseconds": 500, "gateway_performance_stats_output_frequency_seconds": 45, "ws_active": true, "ws_port": "8079", "ws_endpoint": "/gateway", "ws_max_connections": 2, "ws_send_frequency_milliseconds": 5000, "ws_heartbeat_send_frequency_milliseconds": 60000, "threshold_device_error_seconds": 90}'),
  (10,5,'{"log_level": "error", "log_filename": "log/gateway-app.log", "log_max_backups": 10, "application_version": "deprecated", "log_max_age_in_days": 30, "log_compress_backups": true, "log_file_max_size_mb": 10, "rest_api_device_endpoint": "https://api.dev.synapse-its.app/ingest/v3/state", "send_gateway_logs_to_cloud": false, "edi_device_persist_connection": true, "edi_device_processing_retries": 5, "record_http_requests_to_folder": false, "device_state_send_frequency_seconds": 60, "config_change_check_frequency_seconds": 30, "send_gateway_performance_stats_to_cloud": false, "software_update_check_frequency_seconds": 60, "channel_state_send_frequency_milliseconds": 500, "gateway_performance_stats_output_frequency_seconds": 45, "ws_active": true, "ws_port": "8090", "ws_endpoint": "/gateway", "ws_max_connections": 2, "ws_send_frequency_milliseconds": 5000, "ws_heartbeat_send_frequency_milliseconds": 60000, "threshold_device_error_seconds": 90}'),
  (11,5,'{"log_level": "error", "log_filename": "log/gateway-app.log", "log_max_backups": 10, "application_version": "deprecated", "log_max_age_in_days": 30, "log_compress_backups": true, "log_file_max_size_mb": 10, "rest_api_device_endpoint": "https://api.dev.synapse-its.app/ingest/v3/state", "send_gateway_logs_to_cloud": false, "edi_device_persist_connection": true, "edi_device_processing_retries": 5, "record_http_requests_to_folder": false, "device_state_send_frequency_seconds": 60, "config_change_check_frequency_seconds": 30, "send_gateway_performance_stats_to_cloud": false, "software_update_check_frequency_seconds": 60, "channel_state_send_frequency_milliseconds": 500, "gateway_performance_stats_output_frequency_seconds": 45, "ws_active": true, "ws_port": "8100", "ws_endpoint": "/gateway", "ws_max_connections": 2, "ws_send_frequency_milliseconds": 5000, "ws_heartbeat_send_frequency_milliseconds": 60000, "threshold_device_error_seconds": 90}'),
  (12,5,'{"log_level": "error", "log_filename": "log/gateway-app.log", "log_max_backups": 10, "application_version": "deprecated", "log_max_age_in_days": 30, "log_compress_backups": true, "log_file_max_size_mb": 10, "rest_api_device_endpoint": "https://api.dev.synapse-its.app/ingest/v3/state", "send_gateway_logs_to_cloud": false, "edi_device_persist_connection": true, "edi_device_processing_retries": 5, "record_http_requests_to_folder": false, "device_state_send_frequency_seconds": 60, "config_change_check_frequency_seconds": 30, "send_gateway_performance_stats_to_cloud": false, "software_update_check_frequency_seconds": 60, "channel_state_send_frequency_milliseconds": 500, "gateway_performance_stats_output_frequency_seconds": 45, "ws_active": true, "ws_port": "8100", "ws_endpoint": "/gateway", "ws_max_connections": 2, "ws_send_frequency_milliseconds": 5000, "ws_heartbeat_send_frequency_milliseconds": 60000, "threshold_device_error_seconds": 90}');

INSERT INTO {{Device}} (Id,DeviceIdentifier,SoftwareGatewayId,Description,Type) VALUES
  (1, '3fbe60189eef4bb6ab9c03663cc7b3ba', 1, 'Westheimer @ Briargreen', 'Stuttgart'),
  (2, 'fdf164e648994725bfae5141d78887d4', 1, 'Westheimer @ Highway 6', 'Windows'),
  (3, '426b0919812b490d8b48b0ac788b9712', 1, 'Westheimer @ Westhollow Dr', 'RaspberryPi'),
  (5, '88bb06efb418480985a7fabd498eda73', 1, 'Westheimer @ Panagard Dr', 'NA'),
  (6, 'a48afd3cb56b43539b79efb5a25d28ef', 1, 'Westheimer @ Windchase Blvd', 'NA'),
  (7, 'd193181aee3b435397d072f0830377d2', 1, 'Westheimer @ Polk Rd', 'NA'),
  (8, '17dac2eb51904e83969a647573e8ff4d', 1, 'Westheimer @ Eldridge Rd', 'NA'),
  (9, '858aea13d16b40858bbc46e62cd0e813', 1, 'Westheimer @ Briargreen', 'NA'),
  (10, '13cc762a87d042bc8528ef97bbd71a62', 1, 'Westheimer @ Briarwest Blvd', 'NA'),
  (11, 'efda66b77cee49c9a1f730d67012b1e0', 1, 'Westheimer @ S. Dairy Ashford', 'NA'),
  (12, '47b074c6d505401fb3d658d28a88618c', 1, 'Westheimer @ Shadow Briar Dr', 'NA'),
  (13, '9ba25672938e4bd299ad6713e3471084', 1, 'Westheimer @ W Houston Center Blvd', 'NA'),
  (14, 'b604e6be51d64105a47efc0e08cc5f64', 1, 'Westheimer @ Kirkwood', 'NA'),
  (15, '423465d99b34434c8b5fe535fc655630', 1, 'Westheimer @ Crescent Park Dr', 'NA'),
  (16, '7b8a5caff1c94b63bafbc8b0965ed8bc', 1, 'Westheimer @ Woodland Park Dr', 'NA'),
  (17, '82d71c298dba4686af476295aedbd5d7', 1, 'Westheimer @ Hayes Rd', 'NA'),
  (18, 'e2e8319871c7490d9ae814b92fe1a515', 1, 'Westheimer @ Wilcrest Dr', 'NA'),
  (19, 'aeef2063e8bb46f0b0841dfa41f6bc8d', 1, 'Westheimer @ Walnut Bend Ln', 'NA'),
  (20, '7181bf566c6249b7b58ca899ee8054ec', 1, 'Westheimer @ Rogersdale', 'NA'),
  (21, '7fb8f8fe1fc243019a2b2f155f6eaaf9', 1, 'Westheimer @ Beltway 8', 'NA'),
  (10000,'TAM-MMU2-16LEip',10,'Tam - MMU2-16LEip','NA'),
  (10001,'TAM-2010-ECL',10,'Tam - ECL2010','NA'),
  (10002,'TAM-CMU-2212',10,'Tam - CMU2212','NA'),
  (11000,'DUC-MMU2-16LEip',11,'Duc - MMU2-16LEip','NA'),
  (11001,'DUC-2010-ECL',11,'Duc - ECL2010','NA'),
  (11002,'DUC-CMU-2212',11,'Duc - CMU2212','NA'),
  (12000,'MINH-MMU2-16LEip',12,'Minh - MMU2-16LEip','NA'),
  (12001,'MINH-2010-ECL',12,'Minh - ECL 2010','NA'),
  (12002,'MINH-CMU-2212',12,'Minh - CMU2212','NA');

INSERT INTO {{DeviceCommunicationConfig}} (SoftwareGatewayId,DeviceIdentifier,Latitude,Longitude,IPAddress,Port,FlushConnectionMs,EnableRealtime,IsEnabled,Description) VALUES
  (1,'3fbe60189eef4bb6ab9c03663cc7b3ba','29.7601','-95.3701','127.0.0.1','8081','500','true',1,'Mock Device Local'),
  (1,'fdf164e648994725bfae5141d78887d4','29.7601','-95.5701','127.0.0.1','8082','500','true',1,'Mock Device Local'),
  (1,'426b0919812b490d8b48b0ac788b9712','29.7601','-95.4701','127.0.0.1','8083','500','true',1,'Mock Device Local'),
  (1,'CMU2212 Lab','32.8888','-96.7470','**********','10001','500','false',0,'Plano Lab Device'),
  (1,'ECL2010 TypeF+ Lab','33.0918','-96.6989','**********','10001','500','true',0,'Plano Lab Device'),
  (1,'MMU2-16LEip Lab','32.7767','32.7767','**********','10001','500','true',0,'Plano Lab Device'),
  (10,'TAM-2010-ECL','29.7601','-95.4701','127.0.0.1','8092','500','false',1,'Mock Device Local'),
  (10,'TAM-CMU-2212','29.7601','-95.5701','127.0.0.1','8093','500','false',1,'Mock Device Local'),
  (10,'TAM-MMU2-16LEip','29.7601','-95.3701','127.0.0.1','8091','500','false',1,'Mock Device Local'),
  (11,'DUC-2010-ECL','29.7601','-95.4701','127.0.0.1','8102','500','false',1,'Mock Device Local'),
  (11,'DUC-CMU-2212','29.7601','-95.5701','127.0.0.1','8103','500','false',1,'Mock Device Local'),
  (11,'DUC-MMU2-16LEip','29.7601','-95.3701','127.0.0.1','8101','500','false',1,'Mock Device Local'),
  (12,'MINH-2010-ECL','29.7601','-95.4701','127.0.0.1','8202','500','false',1,'Mock Device Local'),
  (12,'MINH-CMU-2212','29.7601','-95.5701','127.0.0.1','8203','500','false',1,'Mock Device Local'),
  (12,'MINH-MMU2-16LEip','29.7601','-95.3701','127.0.0.1','8201','500','false',1,'Mock Device Local');

-- Test User: Email=<EMAIL>, Password=puppies1234, Id=1
--   Used in TestAuthenticate
INSERT INTO {{User}} (Id,OrganizationId,RoleId,UserName,Password,FirstName,LastName,Email,Mobile,NotificationSmsEnabled,IANATimezone,Description,FailedLoginAttempts,IsEnabled,LastLoginUTC,TokenDurationHours,WebEnabled,WebTokenDurationSeconds) VALUES 
  (1,1,1,'<EMAIL>','336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29','Test','User','<EMAIL>','19405551234',false,'America/Chicago','na',0,1,'2025-01-13 14:31:18',2160,1,120);
-- Device instructions (needs to be after user due to foreign key constraints)
INSERT INTO {{SoftwareGatewayInstruction}} (UserId,DeviceId,Instruction,DateQueuedUTC,DateReceivedUTC,Status) VALUES
  (1,1,'get_device_logs',CURRENT_TIMESTAMP,null,'queued'),
  (1,2,'get_device_logs',CURRENT_TIMESTAMP,CURRENT_TIMESTAMP,'received');
-- Firebase Config
INSERT INTO {{Config}} (Key,Value,Description) VALUES 
  ('FirebaseAuth','<real firebase key>','Service account information for Google Firebase.');
-- Test user permissions
INSERT INTO {{UserDevice}} (userid, deviceid, roleid) VALUES 
  (1,1,1),
  (1,2,1),
  (1,3,1);
-- Test monitor name record
INSERT INTO {{DeviceMonitorName}} (DeviceIdentifier, MonitorId, MonitorName) VALUES 
  ('3fbe60189eef4bb6ab9c03663cc7b3ba', 272, 'Test device 1'),
  ('426b0919812b490d8b48b0ac788b9712', 999, 'Test device 2');
-- Test rms engine record
INSERT INTO {{DeviceRMSEngine}} (DeviceIdentifier, EngineVersion, EngineRevision) VALUES 
  ('3fbe60189eef4bb6ab9c03663cc7b3ba', 1, 2),
  ('426b0919812b490d8b48b0ac788b9712', 3, 4);
-- Test device log
INSERT INTO {{DeviceLog}} (DeviceIdentifier, DateUploadedUTC, UserUploaded, LogUUID) VALUES 
  ('3fbe60189eef4bb6ab9c03663cc7b3ba', '2025-05-19T23:55:50Z', 1, '550e8400-e29b-41d4-a716-************'),
  ('426b0919812b490d8b48b0ac788b9712', '2025-06-19T23:55:50Z', 1, '992e8400-e29b-41d4-a716-************');

-- Test User 1 
INSERT INTO {{User}} (Id,OrganizationId,RoleId,UserName,Password,FirstName,LastName,Email,Mobile,NotificationSmsEnabled,IANATimezone,Description,FailedLoginAttempts,IsEnabled,LastLoginUTC,TokenDurationHours,WebEnabled,WebTokenDurationSeconds) VALUES (2,1,1,'<EMAIL>','336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29','Test1','User','<EMAIL>','19405551234',false,'America/Chicago','na',0,1,'2025-01-13 14:31:18',2160,1,120);
-- Test user 1 permissions
INSERT INTO {{UserDevice}} (userid, deviceid, roleid) VALUES (2,1,1);
-- Test Instruction: deviceId=1
INSERT INTO {{SoftwareGatewayInstruction}} (UserId,DeviceId,Instruction,DateQueuedUTC,DateReceivedUTC,Status) VALUES (2,1,'get_device_logs',CURRENT_TIMESTAMP,null,'queued');
-- Test user 1 tokens
INSERT INTO {{UserToken}} (userid, jwttoken, jwttokensha256, createdutc, expirationutc) VALUES (2,'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XFgUWHHtITccNQc-hnZRqXRkPLGz2eY0Q9SQANGCTp4Xv1oWaIZcSk_lxRe-Szd0p5kw0qe05KdMDZD2zF-zJ7-L9enMBOg-UmcdgRlogTq7TP5Is2a9EJ1KWgDCNHMvUi_u8U8qjR1Hvrn6lXBad5DfbhyTJ-HZNwAbfaAT0h_YJ73rHRCAAG0jXXo44u4m5CnHP0HhHXkGCznntsu0bgcsS5fbyqnSIhYxcXg14G2o0eh8GO3QCijr_HwcdutAjGqIL5zEBcBKceWvt4WTENHMH-0HHeMLjn1xpbXMJixm4xhvdcGH3pmDyj8lVT7uKWc6YnRPHPTf7KJdmRaLkw','d2cbd89168cfcf5d950ce54d85e848550c8ba9d0cbd838279e4b7adb92c49645','2025-06-05 15:55:44.391','2030-09-03 15:55:44.389');
