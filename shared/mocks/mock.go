package mocks

import (
	redismock "github.com/go-redis/redismock/v9"

	connect "synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/bqbatcher"
	"synapse-its.com/shared/mocks/bqexecutor"
	"synapse-its.com/shared/mocks/dbexecutor"
	"synapse-its.com/shared/mocks/healthz"
	"synapse-its.com/shared/mocks/pubsub"
	"synapse-its.com/shared/mocks/schemaexecutor"
)

// FakeBigQueryExecutor mocks /shared/connect/bigquery
type FakeBigQueryExecutor = bqexecutor.FakeBigQueryExecutor

// FakeDBExecutor mocks /shared/connect/postgres
type FakeDBExecutor = dbexecutor.FakeDBExecutor

// FakeHealthzServer mocks /shared/healthz
type FakeHealthzServer = healthz.FakeHealthzServer

// FakePubsubClient mocks /shared/connect/pubsub
type (
	FakePubsubClient = pubsub.FakePubsubClient
	FakePubsubTopic  = pubsub.FakePubsubTopic
)

// Creates a new FakePubsubClient
var NewFakePubsubClient = pubsub.NewFakePubsubClient

// FakeBatcher mocks /shared/bqbatch
type (
	FakeBatcher     = bqbatcher.FakeBatcher
	FakeBatchOption = bqbatcher.FakeBatchOption
)

var (
	FakeBatch              = bqbatcher.FakeBatch
	FakeBatcherWithOptions = bqbatcher.FakeBatcherWithOptions
	WithBatchShutdownError = bqbatcher.WithBatchShutdownError
)

type FakeSchemaMigrationExecutor = schemaexecutor.FakeSchemaMigrationExecutor

// FakeConns returns a valid *connect.Connections with a default FakeDBExecutor.
func FakeConns() *connect.Connections {
	// Create a fake Redis client with redismock.
	redisClient, redisMock := redismock.NewClientMock()
	// Set up an expectation that a Ping will be issued, and return "PONG".
	redisMock.ExpectPing().SetVal("PONG")

	return &connect.Connections{
		Redis:    redisClient,
		Pubsub:   NewFakePubsubClient(),
		Postgres: &FakeDBExecutor{},
		Bigquery: &FakeBigQueryExecutor{},
	}
}
