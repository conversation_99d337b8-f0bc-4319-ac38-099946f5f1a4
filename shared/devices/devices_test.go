package devices

import (
	"errors"
	"reflect"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	cmu2212 "synapse-its.com/shared/devices/edi/edicmu2212"
	mmu216le "synapse-its.com/shared/devices/edi/edimmu16le"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func Test_GetDeviceModel(t *testing.T) {
	// Table-driven test for GetDeviceModel function
	tests := []struct {
		name         string                 // Test case name
		model        edihelper.MonitorModel // Input model
		wantTypeName string                 // Expected type name (empty => expect nil)
		wantType     interface{}            // Expected concrete type
	}{
		{
			name:         "UnknownModel",
			model:        0x99,
			wantTypeName: "",
			wantType:     nil,
		},
		{
			name:         "Ecl2010",
			model:        edihelper.Ecl2010,
			wantTypeName: "",
			wantType:     nil,
		},
		{
			name:         "<PERSON><PERSON>2212",
			model:        edihelper.CMUip2212_hv,
			wantTypeName: "EDICMU2212",
			wantType:     &cmu2212.EDICMU2212{},
		},
		{
			name:         "<PERSON>mu16<PERSON>",
			model:        edihelper.Mmu16le,
			wantTypeName: "EDIMMU216LE",
			wantType:     &mmu216le.EDIMMU216LE{},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Enable parallel test execution
			t.Parallel()

			// Create header record with the test model
			hdr := edihelper.HeaderRecord{Model: tc.model}
			dev, err := GetDeviceModel(hdr)

			// Check nil case
			if tc.wantTypeName == "" {
				// should return nil device and ErrNotImplemented
				if dev != nil {
					t.Fatalf("GetDeviceModel(%#x) = %T, want nil", tc.model, dev)
				}
				if !errors.Is(err, ErrNotImplemented) {
					t.Errorf("GetDeviceModel(%#x) error = %v; want ErrNotImplemented", tc.model, err)
				}
				return
			}

			// Check non-nil case
			require.NotNil(t, dev, "GetDeviceModel(%#x) should not return nil", tc.model)

			// Verify type name
			gotType := reflect.TypeOf(dev).Elem().Name()
			assert.Equal(t, tc.wantTypeName, gotType, "GetDeviceModel(%#x): type should be %q", tc.model, tc.wantTypeName)

			// Verify concrete type for non-nil cases
			if tc.wantType != nil {
				expectedType := reflect.TypeOf(tc.wantType).Elem()
				actualType := reflect.TypeOf(dev).Elem()
				assert.Equal(t, expectedType, actualType, "Expected type %v, got %v", expectedType, actualType)
			}
		})
	}
}

// makeMMU216LEHeaderBytes returns a minimal 7-byte header
// that ParseHeaderIntoStruct will recognize as an MMU2-16LE.
func makeMMU216LEHeaderBytes() []byte {
	b := make([]byte, 7)
	b[1] = 0                       // CommVersion
	b[2] = byte(edihelper.Mmu16le) // Model
	b[3] = 0                       // FirmwareVersion
	b[4] = 0                       // FirmwareRevision
	// b[5], b[6] may be zero
	return b
}

func Test_ProcessFunctions_ShortMessage(t *testing.T) {
	// Test that all process functions properly handle short messages
	head := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}

	// Table-driven test for all process functions with short messages
	tests := []struct {
		name    string                                        // Test case name
		fn      func(*pubsubdata.HeaderDetails, []byte) error // Function to test
		wantErr bool                                          // Whether an error is expected
	}{
		{
			name: "LogMonitorReset",
			fn: func(h *pubsubdata.HeaderDetails, msg []byte) error {
				_, _, err := ProcessLogMonitorReset(h, msg)
				return err
			},
			wantErr: true,
		},
		{
			name: "LogPreviousFail",
			fn: func(h *pubsubdata.HeaderDetails, msg []byte) error {
				_, _, err := ProcessLogPreviousFail(h, msg)
				return err
			},
			wantErr: true,
		},
		{
			name: "LogACLineEvent",
			fn: func(h *pubsubdata.HeaderDetails, msg []byte) error {
				_, _, err := ProcessLogACLineEvent(h, msg)
				return err
			},
			wantErr: true,
		},
		{
			name: "LogFaultSignalSequence",
			fn: func(h *pubsubdata.HeaderDetails, msg []byte) error {
				_, _, err := ProcessLogFaultSignalSequence(h, msg)
				return err
			},
			wantErr: true,
		},
		{
			name: "LogConfiguration",
			fn: func(h *pubsubdata.HeaderDetails, msg []byte) error {
				_, _, err := ProcessLogConfiguration(h, msg)
				return err
			},
			wantErr: true,
		},
		{
			name: "MonitorIDandName",
			fn: func(h *pubsubdata.HeaderDetails, msg []byte) error {
				_, _, err := ProcessMonitorIDandName(h, msg)
				return err
			},
			wantErr: true,
		},
		{
			name: "RMSEngineData",
			fn: func(h *pubsubdata.HeaderDetails, msg []byte) error {
				_, _, err := ProcessRmsEngineData(h, msg)
				return err
			},
			wantErr: true,
		},
		{
			name: "RMSStatus",
			fn: func(h *pubsubdata.HeaderDetails, msg []byte) error {
				_, _, err := ProcessRmsData(h, msg)
				return err
			},
			wantErr: true,
		},
	}

	// Use a too-short byte slice to simulate invalid messages
	shortMsg := []byte{0x00, 0x01, 0x02}

	// Execute each test case
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Enable parallel test execution
			t.Parallel()

			// Call the function under test
			err := tt.fn(head, shortMsg)

			// Verify the result
			if tt.wantErr {
				assert.Error(t, err, "Expected error for short message")
			} else {
				assert.NoError(t, err, "Expected no error")
			}
		})
	}
}

func Test_ProcessFunctions_PropagateChecksumError(t *testing.T) {
	// Test that all process functions properly propagate checksum errors
	httpHdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}

	// Table-driven test for all process functions with checksum errors
	tests := []struct {
		name        string                 // Test case name
		makeMsg     func() []byte          // Function to create test message
		call        func(msg []byte) error // Function to test
		wantErr     bool                   // Whether an error is expected
		expectedErr error                  // Expected error type
	}{
		{
			name: "LogMonitorReset",
			makeMsg: func() []byte {
				// Length must equal HeaderLength+N*RecordSize+2 == 7+0+2 == 9
				msg := makeMMU216LEHeaderBytes()
				msg = append(msg, 0) // recordCount @ index 7
				msg = append(msg, 0) // padding to len=9
				return msg
			},
			call: func(msg []byte) error {
				_, _, err := ProcessLogMonitorReset(httpHdr, msg)
				return err
			},
			wantErr:     true,
			expectedErr: edihelper.ErrMsgByteChecksum,
		},
		{
			name: "LogPreviousFail",
			makeMsg: func() []byte {
				// Length = 7+0*98+2 = 9
				msg := makeMMU216LEHeaderBytes()
				msg = append(msg, 0)
				msg = append(msg, 0)
				return msg
			},
			call: func(msg []byte) error {
				_, _, err := ProcessLogPreviousFail(httpHdr, msg)
				return err
			},
			wantErr:     true,
			expectedErr: edihelper.ErrMsgByteChecksum,
		},
		{
			name: "LogACLineEvent",
			makeMsg: func() []byte {
				// Length = 7+0*8+2 = 9
				msg := makeMMU216LEHeaderBytes()
				msg = append(msg, 0)
				msg = append(msg, 0)
				return msg
			},
			call: func(msg []byte) error {
				_, _, err := ProcessLogACLineEvent(httpHdr, msg)
				return err
			},
			wantErr:     true,
			expectedErr: edihelper.ErrMsgByteChecksum,
		},
		{
			name: "LogFaultSignalSequence",
			makeMsg: func() []byte {
				// HeaderLength=7, numberOfRecords @ index 9, length = 7+2+0*12+2 = 11
				msg := makeMMU216LEHeaderBytes()
				msg = append(msg, 0, 0) // positions 7,8
				msg = append(msg, 0)    // recordCount @ pos 9
				msg = append(msg, 0)    // padding to len=11
				return msg
			},
			call: func(msg []byte) error {
				_, _, err := ProcessLogFaultSignalSequence(httpHdr, msg)
				return err
			},
			wantErr:     true,
			expectedErr: edihelper.ErrMsgByteChecksum,
		},
		{
			name: "LogConfiguration",
			makeMsg: func() []byte {
				// Length = 7+0*64+2 = 9
				msg := makeMMU216LEHeaderBytes()
				msg = append(msg, 0)
				msg = append(msg, 0)
				return msg
			},
			call: func(msg []byte) error {
				_, _, err := ProcessLogConfiguration(httpHdr, msg)
				return err
			},
			wantErr:     true,
			expectedErr: edihelper.ErrMsgByteChecksum,
		},
		{
			name: "MonitorIDandName",
			makeMsg: func() []byte {
				// Length = 7 + MonitorNameLength(30) + 1 = 38
				msg := makeMMU216LEHeaderBytes()
				// Pad out to 38 bytes total:
				msg = append(msg, make([]byte, 38-len(msg))...)
				return msg
			},
			call: func(msg []byte) error {
				_, _, err := ProcessMonitorIDandName(httpHdr, msg)
				return err
			},
			wantErr:     true,
			expectedErr: edihelper.ErrMsgByteChecksum,
		},
		{
			name: "RMSEngineData",
			makeMsg: func() []byte {
				// ExpectedLength = 20
				msg := makeMMU216LEHeaderBytes()
				// Pad out to 20 bytes total:
				msg = append(msg, make([]byte, 20-len(msg))...)
				return msg
			},
			call: func(msg []byte) error {
				_, _, err := ProcessRmsEngineData(httpHdr, msg)
				return err
			},
			wantErr:     true,
			expectedErr: edihelper.ErrMsgByteChecksum,
		},
		{
			name: "RMSStatus",
			makeMsg: func() []byte {
				// ExpectedLength = 106
				msg := makeMMU216LEHeaderBytes()
				// Pad out to 106 bytes total:
				msg = append(msg, make([]byte, 106-len(msg))...)
				return msg
			},
			call: func(msg []byte) error {
				_, _, err := ProcessRmsData(httpHdr, msg)
				return err
			},
			wantErr:     true,
			expectedErr: edihelper.ErrMsgByteChecksum,
		},
	}

	// Execute each test case
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Enable parallel test execution
			t.Parallel()

			// Create test message
			msg := tc.makeMsg()

			// Call the function under test
			err := tc.call(msg)

			// Verify error presence
			if tc.wantErr {
				assert.Error(t, err, "Expected an error")
				assert.ErrorIs(t, err, tc.expectedErr, "Expected error %v", tc.expectedErr)
			} else {
				assert.NoError(t, err, "Expected no error")
			}
		})
	}
}

func TestProcessFunctions_NotImplementedError(t *testing.T) {
	origParseHeader := parseHeaderIntoStruct
	defer func() { parseHeaderIntoStruct = origParseHeader }()
	parseHeaderIntoStruct = func(headerBytes []byte) (*edihelper.HeaderRecord, error) {
		return &edihelper.HeaderRecord{
			Model: edihelper.Nomodel,
		}, nil
	}
	// stub out checksum so every message "validates"
	origValidate := edihelper.ValidateChecksum
	defer func() { edihelper.ValidateChecksum = origValidate }()
	edihelper.ValidateChecksum = func(_ []byte) error { return nil }

	httpHdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}

	type scenario struct {
		name    string
		makeMsg func() []byte
		call    func(msg []byte) (interface{}, *edihelper.HeaderRecord, error)
	}

	scenarios := []scenario{
		{
			name: "ProcessLogMonitorReset",
			makeMsg: func() []byte {
				return makeMMU216LEHeaderBytes()
			},
			call: func(msg []byte) (interface{}, *edihelper.HeaderRecord, error) {
				return ProcessLogMonitorReset(httpHdr, msg)
			},
		},
		{
			name: "ProcessLogPreviousFail",
			makeMsg: func() []byte {
				return makeMMU216LEHeaderBytes()
			},
			call: func(msg []byte) (interface{}, *edihelper.HeaderRecord, error) {
				return ProcessLogPreviousFail(httpHdr, msg)
			},
		},
		{
			name: "ProcessLogACLineEvent",
			makeMsg: func() []byte {
				return makeMMU216LEHeaderBytes()
			},
			call: func(msg []byte) (interface{}, *edihelper.HeaderRecord, error) {
				return ProcessLogACLineEvent(httpHdr, msg)
			},
		},
		{
			name: "ProcessLogFaultSignalSequence",
			makeMsg: func() []byte {
				return makeMMU216LEHeaderBytes()
			},
			call: func(msg []byte) (interface{}, *edihelper.HeaderRecord, error) {
				return ProcessLogFaultSignalSequence(httpHdr, msg)
			},
		},
		{
			name: "ProcessLogConfiguration",
			makeMsg: func() []byte {
				return makeMMU216LEHeaderBytes()
			},
			call: func(msg []byte) (interface{}, *edihelper.HeaderRecord, error) {
				return ProcessLogConfiguration(httpHdr, msg)
			},
		},
		{
			name: "ProcessMonitorIDandName",
			makeMsg: func() []byte {
				return makeMMU216LEHeaderBytes()
			},
			call: func(msg []byte) (interface{}, *edihelper.HeaderRecord, error) {
				return ProcessMonitorIDandName(httpHdr, msg)
			},
		},
		{
			name: "ProcessRmsEngineData",
			makeMsg: func() []byte {
				return makeMMU216LEHeaderBytes()
			},
			call: func(msg []byte) (interface{}, *edihelper.HeaderRecord, error) {
				return ProcessRmsEngineData(httpHdr, msg)
			},
		},
		{
			name: "ProcessRmsData",
			makeMsg: func() []byte {
				return makeMMU216LEHeaderBytes()
			},
			call: func(msg []byte) (interface{}, *edihelper.HeaderRecord, error) {
				return ProcessRmsData(httpHdr, msg)
			},
		},
	}

	for _, sc := range scenarios {
		t.Run(sc.name, func(t *testing.T) {
			_, _, err := sc.call(sc.makeMsg())
			// must be ErrNotImplemented
			if !errors.Is(err, ErrNotImplemented) {
				t.Fatalf("%s: error = %v; want ErrNotImplemented", sc.name, err)
			}
		})
	}
}

func buildFaultMsg(numRecords int, faultType byte) []byte {
	const headerLen = 7
	const traceLen = 12

	// total length = headerLen + numRecords*traceLen + 1(checksum)
	total := headerLen + 2 + numRecords*traceLen + 2
	msg := make([]byte, total)
	msg[0] = 0x9C
	msg[1] = 0x38
	msg[2] = 0x0B
	msg[3] = 0x01
	msg[4] = 0x75
	msg[5] = 0x00
	msg[6] = 0x00
	// byte 8 = faultType,  byte 9 = numberOfRecords
	msg[8] = faultType
	msg[9] = byte(numRecords)

	return msg
}

func computeValidChecksum(t *testing.T, msg []byte) {
	for b := 0; b < 256; b++ {
		msg[len(msg)-1] = byte(b)
		if err := edihelper.ValidateChecksum(msg); err == nil {
			return
		}
	}
	t.Fatal("unable to find a valid checksum for test message")
}

func TestProcessFunctions_SuccessPath(t *testing.T) {
	// stub out checksum so every message "validates"
	origValidate := edihelper.ValidateChecksum
	defer func() { edihelper.ValidateChecksum = origValidate }()
	edihelper.ValidateChecksum = func(_ []byte) error { return nil }

	httpHdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}

	type scenario struct {
		name    string
		makeMsg func() []byte
		call    func(msg []byte) (interface{}, *edihelper.HeaderRecord, error)
	}

	scenarios := []scenario{
		{
			name: "ProcessLogMonitorReset",
			makeMsg: func() []byte {
				// length = 7 + 0*7 + 2 = 9
				msg := makeMMU216LEHeaderBytes()
				return append(msg, 0, 0)
			},
			call: func(msg []byte) (interface{}, *edihelper.HeaderRecord, error) {
				return ProcessLogMonitorReset(httpHdr, msg)
			},
		},
		{
			name: "ProcessLogPreviousFail",
			makeMsg: func() []byte {
				// length = 7 + 0*98 + 2 = 9
				msg := makeMMU216LEHeaderBytes()
				return append(msg, 0, 0)
			},
			call: func(msg []byte) (interface{}, *edihelper.HeaderRecord, error) {
				return ProcessLogPreviousFail(httpHdr, msg)
			},
		},
		{
			name: "ProcessLogACLineEvent",
			makeMsg: func() []byte {
				// length = 7 + 0*8 + 2 = 9
				msg := makeMMU216LEHeaderBytes()
				return append(msg, 0, 0)
			},
			call: func(msg []byte) (interface{}, *edihelper.HeaderRecord, error) {
				return ProcessLogACLineEvent(httpHdr, msg)
			},
		},
		{
			name: "ProcessLogFaultSignalSequence",
			makeMsg: func() []byte {
				msg := buildFaultMsg(2, 1) // faultType=1 -> "CVM Fault"
				// set a “normal” timestamp of 100 (0x00,0x64) in the first two trace bytes
				msg[10] = 0x00
				msg[11] = 0x64
				// leave all other trace‐status bytes at 0
				for i := 12; i < 10+12; i++ {
					msg[i] = 0
				}
				msg[23] = 0x00
				msg[24] = 0x64
				// leave all other trace‐status bytes at 0
				for i := 25; i < 23+12; i++ {
					msg[i] = 0
				}
				computeValidChecksum(t, msg)
				return msg
			},
			call: func(msg []byte) (interface{}, *edihelper.HeaderRecord, error) {
				return ProcessLogFaultSignalSequence(httpHdr, msg)
			},
		},
		{
			name: "ProcessLogConfiguration",
			makeMsg: func() []byte {
				// length = 7 + 0*64 + 2 = 9
				msg := makeMMU216LEHeaderBytes()
				return append(msg, 0, 0)
			},
			call: func(msg []byte) (interface{}, *edihelper.HeaderRecord, error) {
				return ProcessLogConfiguration(httpHdr, msg)
			},
		},
		{
			name: "ProcessMonitorIDandName",
			makeMsg: func() []byte {
				// length = 7 + 30 + 1 = 38
				msg := makeMMU216LEHeaderBytes()
				return append(msg, make([]byte, 31)...)
			},
			call: func(msg []byte) (interface{}, *edihelper.HeaderRecord, error) {
				return ProcessMonitorIDandName(httpHdr, msg)
			},
		},
		{
			name: "ProcessRmsEngineData",
			makeMsg: func() []byte {
				// expectedLength = 20
				msg := []byte{
					0x91, 0x38, 0x0b, 0x01, 0x75, 0x00, 0x00, 0x05, 0x31, 0x7F, // from *-request2*
					0x93, 0x38, 0x0b, 0x01, 0x75, 0x00, 0x00, 0x04, 0x01, 0xAE, // from *-request1*
				}
				return msg
			},
			call: func(msg []byte) (interface{}, *edihelper.HeaderRecord, error) {
				return ProcessRmsEngineData(httpHdr, msg)
			},
		},
		{
			name: "ProcessRmsData",
			makeMsg: func() []byte {
				// expectedLength = 106, and we need valid BCD date at bytes 39–44
				msg := makeMMU216LEHeaderBytes()
				// pad to 106
				buf := append(msg, make([]byte, 106-len(msg))...)
				// BCD date: sec@39,min@40,hr@41,day@42,month@43,year@44
				buf[39] = 0x00 // sec=00
				buf[40] = 0x00 // min=00
				buf[41] = 0x00 // hr=00
				buf[42] = 0x01 // day=01
				buf[43] = 0x01 // month=01
				buf[44] = 0x00 // year=00 -> 2000
				return buf
			},
			call: func(msg []byte) (interface{}, *edihelper.HeaderRecord, error) {
				return ProcessRmsData(httpHdr, msg)
			},
		},
	}

	for _, sc := range scenarios {
		t.Run(sc.name, func(t *testing.T) {
			rec, hdr, err := sc.call(sc.makeMsg())
			assert.NoError(t, err, "Expected success")
			require.NotNil(t, hdr, "Expected non-nil headerDetails")
			assert.Equal(t, edihelper.Mmu16le, hdr.Model, "Expected Model=Mmu16le")
			assert.NotNil(t, rec, "Expected non-nil records for %s", sc.name)
			// a little extra check for RMSStatus’s MonitorTime:
			if sc.name == "ProcessRmsData" {
				rs := rec.(*edihelper.RmsStatusRecord)
				expectedTime := time.Date(2000, 1, 1, 0, 0, 0, 0, time.UTC)
				assert.True(t, rs.MonitorTime.Equal(expectedTime),
					"Expected MonitorTime %v, got %v", expectedTime, rs.MonitorTime)
			}
		})
	}
}
