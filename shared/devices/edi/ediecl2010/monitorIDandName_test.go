package ediecl2010

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func TestMonitorIDandName(t *testing.T) {
	tests := []struct {
		name           string
		byteMsg        []byte
		httpHeader     *pubsubdata.HeaderDetails
		header         *helper.HeaderRecord
		expectedError  error
		validateRecord func(t *testing.T, monitor *helper.MonitorNameAndId)
	}{
		{
			name: "nil header",
			byteMsg: func() []byte {
				msgLen := HeaderLength + K2018IDNameLength + ChecksumLength
				msg := make([]byte, msgLen)
				msg[0] = 0x01 // Message type
				msg[1] = 0x02 // Device address
				msg[2] = 0x03 // Command code
				msg[3] = 0x00 // Response status
				msg[5] = 0x01 // Monitor ID ls byte
				msg[6] = 0x00 // Monitor ID ms byte
				// Calculate checksum
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header:        nil,
			expectedError: helper.ErrMsgHeaderRecordNil,
		},
		{
			name: "invalid checksum",
			byteMsg: func() []byte {
				msgLen := HeaderLength + K2018IDNameLength + ChecksumLength
				msg := make([]byte, msgLen)
				msg[0] = 0x01          // Message type
				msg[1] = 0x02          // Device address
				msg[2] = 0x03          // Command code
				msg[3] = 0x00          // Response status
				msg[5] = 0x01          // Monitor ID ls byte
				msg[6] = 0x00          // Monitor ID ms byte
				msg[len(msg)-1] = 0xFF // Invalid checksum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:            helper.Ecl2010,
				FirmwareRevision: 0x51,
				CommVersion:      0x27,
			},
			expectedError: helper.ErrMsgByteChecksum,
		},
		{
			name: "invalid message length",
			byteMsg: func() []byte {
				msgLen := HeaderLength + K2018IDNameLength + ChecksumLength - 1 // One byte too short
				msg := make([]byte, msgLen)
				msg[0] = 0x01 // Message type
				msg[1] = 0x02 // Device address
				msg[2] = 0x03 // Command code
				msg[3] = 0x00 // Response status
				msg[5] = 0x01 // Monitor ID ls byte
				msg[6] = 0x00 // Monitor ID ms byte
				// Calculate checksum
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:            helper.Ecl2010,
				FirmwareRevision: 0x51,
				CommVersion:      0x27,
			},
			expectedError: helper.ErrMsgByteLen,
		},
		{
			name: "valid message with firmware revision > 0x50",
			byteMsg: func() []byte {
				msgLen := HeaderLength + K2018IDNameLength + K2018IDNameOffset + ChecksumLength
				msg := make([]byte, msgLen)
				msg[0] = 0x01 // Message type
				msg[1] = 0x02 // Device address
				msg[2] = 0x03 // Command code
				msg[3] = 0x00 // Response status
				msg[5] = 0x01 // Monitor ID ls byte
				msg[6] = 0x00 // Monitor ID ms byte
				// Set monitor name
				copy(msg[HeaderLength+1:], []byte("Test Monitor Name"))
				// Calculate checksum
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:            helper.Ecl2010,
				FirmwareRevision: 0x51,
				CommVersion:      0x27,
			},
			expectedError: nil,
			validateRecord: func(t *testing.T, monitor *helper.MonitorNameAndId) {
				assert.NotNil(t, monitor)
				assert.Equal(t, DeviceModel, monitor.DeviceModel)
				assert.Equal(t, int64(1), monitor.MonitorId)
				assert.Equal(t, "Test Monitor Name", monitor.MonitorName)
			},
		},
		{
			name: "valid message with firmware revision <= 0x50",
			byteMsg: func() []byte {
				msgLen := HeaderLength + K2018IDNameLength + ChecksumLength
				msg := make([]byte, msgLen)
				msg[0] = 0x01 // Message type
				msg[1] = 0x02 // Device address
				msg[2] = 0x03 // Command code
				msg[3] = 0x00 // Response status
				msg[5] = 0x01 // Monitor ID ls byte
				msg[6] = 0x00 // Monitor ID ms byte
				// Calculate checksum
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:            helper.Ecl2010,
				FirmwareRevision: 0x50,
				CommVersion:      0x27,
			},
			expectedError: nil,
			validateRecord: func(t *testing.T, monitor *helper.MonitorNameAndId) {
				assert.NotNil(t, monitor)
				assert.Equal(t, DeviceModel, monitor.DeviceModel)
				assert.Equal(t, int64(0), monitor.MonitorId)
				assert.Equal(t, "", monitor.MonitorName)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			device := EDIECL2010{}
			monitor, err := device.MonitorIDandName(tt.httpHeader, tt.byteMsg, tt.header)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedError)
				assert.Nil(t, monitor)
			} else {
				assert.NoError(t, err)
				if tt.validateRecord != nil {
					tt.validateRecord(t, monitor)
				}
			}
		})
	}
}
