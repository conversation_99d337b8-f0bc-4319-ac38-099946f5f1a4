package ediecl2010

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

// buildValidLogMonitorResetMsg creates a valid log monitor reset message with the specified number of records
func buildValidLogMonitorResetMsg(numRecords int) []byte {
	msg := make([]byte, HeaderLength+numRecords*MR2018LogLength+ChecksumLength)
	msg[HeaderLength-1] = byte(numRecords) // Set number of records

	// Set some sample data for each record
	for i := 0; i < numRecords; i++ {
		offset := HeaderLength + i*MR2018LogLength
		// Set some sample BCD values for datetime
		msg[offset+0] = 0x18 // sec
		msg[offset+1] = 0x16 // min
		msg[offset+2] = 0x09 // hour
		msg[offset+3] = 0x08 // day
		msg[offset+4] = 0x07 // month
		msg[offset+5] = 0x21 // year (2021)
	}

	// Calculate and set checksum
	var sum byte
	for _, b := range msg[:len(msg)-1] {
		sum += b
	}
	msg[len(msg)-1] = ^sum // One's complement of sum

	return msg
}

func TestLogMonitorReset(t *testing.T) {
	validHeader := &helper.HeaderRecord{
		Model: helper.Ecl2010,
	}

	validHTTPHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}

	// Debug: Print the message bytes for a single record
	msg := buildValidLogMonitorResetMsg(1)
	t.Logf("Single record message bytes: %v", msg)
	// Debug: Print model constants
	t.Logf("helper.Ecl2010: %v, helper.CMUip2212_hv: %v", helper.Ecl2010, helper.CMUip2212_hv)

	tests := []struct {
		name        string
		httpHeader  *pubsubdata.HeaderDetails
		byteMsg     []byte
		header      *helper.HeaderRecord
		wantErr     bool
		errContains string
		validate    func(t *testing.T, result *helper.LogMonitorResetRecords)
	}{
		{
			name:        "nil header",
			httpHeader:  validHTTPHeader,
			byteMsg:     buildValidLogMonitorResetMsg(1),
			header:      nil,
			wantErr:     true,
			errContains: "header is nil",
		},
		{
			name:       "invalid device model",
			httpHeader: validHTTPHeader,
			byteMsg:    buildValidLogMonitorResetMsg(1),
			header: &helper.HeaderRecord{
				Model:            helper.CMUip2212_hv,
				FirmwareRevision: 0x51, // > 0x50 to trigger error
			},
			wantErr:     true,
			errContains: "unsupported device",
		},
		{
			name:        "invalid message length",
			httpHeader:  validHTTPHeader,
			byteMsg:     []byte{1, 2, 3}, // Too short
			header:      validHeader,
			wantErr:     true,
			errContains: "checksum failure",
		},
		{
			name:       "valid single record",
			httpHeader: validHTTPHeader,
			byteMsg:    buildValidLogMonitorResetMsg(1),
			header:     validHeader,
			wantErr:    false,
			validate: func(t *testing.T, result *helper.LogMonitorResetRecords) {
				assert.Equal(t, DeviceModel, result.DeviceModel)
				assert.Len(t, result.Records, 1)
				assert.NotNil(t, result.Records[0].DateTime)
				assert.Equal(t, validHTTPHeader.GatewayTimezone, "UTC")
			},
		},
		{
			name:       "valid multiple records",
			httpHeader: validHTTPHeader,
			byteMsg:    buildValidLogMonitorResetMsg(3),
			header:     validHeader,
			wantErr:    false,
			validate: func(t *testing.T, result *helper.LogMonitorResetRecords) {
				assert.Equal(t, DeviceModel, result.DeviceModel)
				assert.Len(t, result.Records, 3)
				for _, record := range result.Records {
					assert.NotNil(t, record.DateTime)
				}
			},
		},
		{
			name:       "zero records",
			httpHeader: validHTTPHeader,
			byteMsg:    buildValidLogMonitorResetMsg(0),
			header:     validHeader,
			wantErr:    false,
			validate: func(t *testing.T, result *helper.LogMonitorResetRecords) {
				assert.Equal(t, DeviceModel, result.DeviceModel)
				assert.Len(t, result.Records, 0)
			},
		},
		{
			name:       "invalid checksum",
			httpHeader: validHTTPHeader,
			byteMsg: func() []byte {
				msg := buildValidLogMonitorResetMsg(1)
				msg[len(msg)-1] = 0xFF // Invalid checksum
				return msg
			}(),
			header:      validHeader,
			wantErr:     true,
			errContains: "checksum failure",
		},
		{
			name:       "invalid BCD month",
			httpHeader: validHTTPHeader,
			byteMsg: func() []byte {
				msg := buildValidLogMonitorResetMsg(1)
				offset := HeaderLength
				msg[offset+4] = 0x1A // Invalid BCD for month
				// recalculate checksum
				var sum byte
				for _, b := range msg[:len(msg)-1] {
					sum += b
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			header:      validHeader,
			wantErr:     true,
			errContains: "bad month - month reported",
		},
		{
			name:       "invalid message length (wrong length, valid checksum)",
			httpHeader: validHTTPHeader,
			byteMsg: func() []byte {
				msg := buildValidLogMonitorResetMsg(1)
				// Remove one byte to make the length invalid
				msg = msg[:len(msg)-2]
				// Recalculate checksum for new length
				var sum byte
				for _, b := range msg[:len(msg)-1] {
					sum += b
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			header:      validHeader,
			wantErr:     true,
			errContains: "error byte length",
		},
	}

	device := EDIECL2010{}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := device.LogMonitorReset(tt.httpHeader, tt.byteMsg, tt.header)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains)
				}
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				if tt.validate != nil {
					tt.validate(t, result)
				}
			}
		})
	}
}
