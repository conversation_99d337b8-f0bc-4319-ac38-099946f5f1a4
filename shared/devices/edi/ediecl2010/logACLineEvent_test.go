package ediecl2010

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func TestLogACLineEvent(t *testing.T) {
	tests := []struct {
		name                  string
		byteMsg               []byte
		header                *helper.HeaderRecord
		httpHeader            *pubsubdata.HeaderDetails
		expectedError         error
		expectedErrorContains string
		validateFunc          func(t *testing.T, result *helper.LogACLineEventRecords)
	}{
		{
			name:       "Valid AC Line Event Single Record",
			byteMsg:    buildValidACLineEventMsg(1),
			header:     createValidACLineHeader(false),
			httpHeader: &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"},
			validateFunc: func(t *testing.T, result *helper.LogACLineEventRecords) {
				require.NotNil(t, result)
				require.Len(t, result.Records, 1)
				require.Equal(t, VoltageTypeAC, result.VoltageType)
				record := result.Records[0]
				require.Equal(t, int64(230), record.LineVoltageRms)
				require.Equal(t, int32(50), *record.LineFrequencyHz)
			},
		},
		{
			name:       "Valid DC Line Event Single Record",
			byteMsg:    buildValidACLineEventMsg(1),
			header:     createValidACLineHeader(true),
			httpHeader: &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"},
			validateFunc: func(t *testing.T, result *helper.LogACLineEventRecords) {
				require.NotNil(t, result)
				require.Equal(t, VoltageTypeDC, result.VoltageType)
			},
		},
		{
			name:                  "Unsupported Device Model (validateDeviceInfo error)",
			byteMsg:               buildValidACLineEventMsg(1),
			header:                &helper.HeaderRecord{Model: helper.CMUip2212_hv, FirmwareRevision: 0x51, MainsDC: false, PowerDownLevel: 100},
			httpHeader:            &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"},
			expectedErrorContains: "unsupported device",
		},
		{
			name:          "Nil Header",
			byteMsg:       buildValidACLineEventMsg(1),
			header:        nil,
			httpHeader:    &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"},
			expectedError: helper.ErrMsgHeaderRecordNil,
		},
		{
			name:          "Invalid Checksum",
			byteMsg:       createInvalidACLineChecksumMsg(),
			header:        createValidACLineHeader(false),
			httpHeader:    &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"},
			expectedError: helper.ErrMsgByteChecksum,
		},
		{
			name:          "Invalid Message Length",
			byteMsg:       []byte{1, 2, 3}, // Too short
			header:        createValidACLineHeader(false),
			httpHeader:    &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"},
			expectedError: helper.ErrMsgByteLen,
		},
		{
			name:          "Invalid Message Length (with valid header and checksum)",
			byteMsg:       createInvalidACLineLengthMsg(),
			header:        createValidACLineHeader(false),
			httpHeader:    &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"},
			expectedError: helper.ErrMsgByteLen,
		},
		{
			name:                  "Invalid BCD in DateTime Field",
			byteMsg:               createInvalidACLineBCDMsg(),
			header:                createValidACLineHeader(false),
			httpHeader:            &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"},
			expectedErrorContains: "invalid BCD",
			validateFunc: func(t *testing.T, result *helper.LogACLineEventRecords) {
				t.Error("Expected error due to invalid BCD, but got result")
			},
		},
		{
			name:       "Multiple Records",
			byteMsg:    buildValidACLineEventMsg(3),
			header:     createValidACLineHeader(false),
			httpHeader: &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"},
			validateFunc: func(t *testing.T, result *helper.LogACLineEventRecords) {
				require.NotNil(t, result)
				require.Len(t, result.Records, 3)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			device := EDIECL2010{}
			result, err := device.LogACLineEvent(tt.httpHeader, tt.byteMsg, tt.header)

			if tt.expectedErrorContains != "" {
				require.Error(t, err)
				require.ErrorContains(t, err, tt.expectedErrorContains)
				return
			}

			if tt.expectedError != nil {
				require.Error(t, err)
				return
			}

			require.NoError(t, err)

			if tt.validateFunc != nil {
				tt.validateFunc(t, result)
			}
		})
	}
}

// Helper functions to create test data

func createValidACLineHeader(mainsDC bool) *helper.HeaderRecord {
	return &helper.HeaderRecord{
		Model:          helper.Ecl2010,
		MainsDC:        mainsDC,
		PowerDownLevel: 100,
	}
}

// Helper to convert int to BCD
func toACLineBCD(val int) byte {
	return byte(((val / 10) << 4) | (val % 10))
}

func buildValidACLineEventMsg(numRecords int) []byte {
	// Create header
	msg := make([]byte, HeaderLength)
	msg[0] = 0x01             // Message type
	msg[1] = 0x02             // Device address
	msg[2] = 0x03             // Command code
	msg[3] = 0x00             // Response status
	msg[7] = byte(numRecords) // Number of records

	// Create records
	for i := 0; i < numRecords; i++ {
		record := make([]byte, AC2018LogLength)
		record[0] = 0x01 // Event type
		record[1] = 230  // Line voltage
		now := time.Now()
		record[2] = toACLineBCD(now.Second())     // Second (BCD)
		record[3] = toACLineBCD(now.Minute())     // Minute (BCD)
		record[4] = toACLineBCD(now.Hour())       // Hour (BCD)
		record[5] = toACLineBCD(now.Day())        // Day (BCD)
		record[6] = toACLineBCD(int(now.Month())) // Month (BCD)
		record[7] = toACLineBCD(now.Year() % 100) // Year (BCD)
		record[8] = 50                            // Line frequency
		msg = append(msg, record...)
	}

	// Calculate checksum
	var sum byte
	for _, b := range msg {
		sum += b
	}
	msg = append(msg, ^sum) // One's complement of sum

	return msg
}

func createInvalidACLineChecksumMsg() []byte {
	msg := buildValidACLineEventMsg(1)
	msg[len(msg)-1] = 0xFF // Invalid checksum
	return msg
}

func createInvalidACLineLengthMsg() []byte {
	msg := buildValidACLineEventMsg(1)
	msg = msg[:len(msg)-2] // Remove 2 bytes to make it invalid length
	// Recalculate checksum for new length
	var sum byte
	for _, b := range msg[:len(msg)-1] {
		sum += b
	}
	msg[len(msg)-1] = ^sum
	return msg
}

func createInvalidACLineBCDMsg() []byte {
	msg := make([]byte, HeaderLength)
	msg[0] = 0x01
	msg[1] = 0x02
	msg[2] = 0x03
	msg[3] = 0x00
	msg[7] = 1 // 1 record

	record := make([]byte, AC2018LogLength)
	record[0] = 0x01            // Event type
	record[1] = 230             // Line voltage
	record[2] = toACLineBCD(30) // Second (BCD)
	record[3] = toACLineBCD(45) // Minute (BCD)
	record[4] = 0x1D            // Invalid BCD for hour (should be <= 0x23 in BCD, but 0x1D is not valid BCD)
	record[5] = toACLineBCD(15) // Day (BCD)
	record[6] = toACLineBCD(6)  // Month (BCD)
	record[7] = toACLineBCD(23) // Year (BCD)
	record[8] = 50              // Line frequency
	msg = append(msg, record...)

	// Calculate checksum
	var sum byte
	for _, b := range msg {
		sum += b
	}
	msg = append(msg, ^sum)
	return msg
}
