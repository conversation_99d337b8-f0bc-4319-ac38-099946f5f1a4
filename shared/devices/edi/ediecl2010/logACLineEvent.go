package ediecl2010

import (
	"fmt"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

const (
	AC2018LogLength = 9
	VoltageTypeAC   = int64(1)
	VoltageTypeDC   = int64(2)
)

/*
Byte Message Layout
==================

Header (8 bytes)
---------------
[0] Message type identifier
[1] Device address
[2] Command code
[3] Response status
[4] Reserved byte
[5] Reserved byte
[6] Reserved byte
[7] Number of records (0-255)

Record Structure (9 bytes per record)
-----------------------------------
[8] Event type
[9] Line voltage (0-999)
[10] Second in BCD format (00-59)
[11] Minute in BCD format (00-59)
[12] Hour in BCD format (00-23)
[13] Day in BCD format (01-31)
[14] Month in BCD format (01-12)
[15] Year in BCD format (00-99)
[16] Line frequency (0-99)

Checksum (1 byte)
----------------
[Last byte] XOR checksum of all previous bytes
*/

// LogACLineEvent parses the AC line event log bytes into a LogACLineEventRecords struct
func (device EDIECL2010) LogACLineEvent(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (allRecords *helper.LogACLineEventRecords, err error) {
	if header == nil {
		return nil, fmt.Errorf("%w header is nil", helper.ErrMsgHeaderRecordNil)
	}

	if err = helper.ValidateChecksum(byteMsg); err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	if err = validateDeviceInfo(header); err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrUnsupportedDevice, err)
	}

	numRecords := int(byteMsg[HeaderLength-1])
	byteMessageLength := HeaderLength + numRecords*AC2018LogLength + ChecksumLength
	allRecords = &helper.LogACLineEventRecords{
		DeviceModel: DeviceModel,
		RawMessage:  byteMsg,
		VoltageType: func() int64 {
			if header.MainsDC {
				return VoltageTypeDC
			}
			return VoltageTypeAC
		}(),
	}

	if !helper.VerifyByteLen(byteMsg, byteMessageLength) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), byteMessageLength)
	}

	for idx := range numRecords {
		record := helper.LogACLineEventRecord{
			LineFrequencyHz: new(int32),
		}
		eventType := byteMsg[HeaderLength+idx*AC2018LogLength+0]
		linevoltage := int64(byteMsg[HeaderLength+idx*AC2018LogLength+1])
		record.DateTime, err = helper.ConvertBCDBytesToDateTimeII(
			byteMsg[HeaderLength+idx*AC2018LogLength+6],
			byteMsg[HeaderLength+idx*AC2018LogLength+5],
			byteMsg[HeaderLength+idx*AC2018LogLength+7],
			byteMsg[HeaderLength+idx*AC2018LogLength+4],
			byteMsg[HeaderLength+idx*AC2018LogLength+3],
			byteMsg[HeaderLength+idx*AC2018LogLength+2],
			httpHeader.GatewayTimezone,
		)
		if err != nil {
			return nil, err
		}
		record.EventType = helper.GetACLineEventType(helper.MonitorModel(header.Model), header.MainsDC, linevoltage, header.PowerDownLevel, eventType)
		record.LineVoltageRms = linevoltage
		*record.LineFrequencyHz = int32(byteMsg[HeaderLength+idx*AC2018LogLength+8])
		allRecords.Records = append(allRecords.Records, record)
	}

	return allRecords, nil
}
