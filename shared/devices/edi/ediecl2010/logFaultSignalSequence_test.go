package ediecl2010

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func TestParseTraceRecord(t *testing.T) {
	tests := []struct {
		name           string
		traceBytes     []byte
		header         *helper.HeaderRecord
		expectedRecord *helper.TraceBuffer
	}{
		{
			name: "valid trace record with all channels enabled",
			traceBytes: []byte{
				0x00, 0x01, // Timestamp: 1
				0xFF, 0xFF, 0xFF, // Red status: all channels enabled
				0xFF, 0xFF, 0xFF, // Yellow status: all channels enabled
				0xFF, 0xFF, 0xFF, // Green status: all channels enabled
				0x01, // EE_SF_RE: true
				0x80, // AC Voltage: 128
			},
			header: &helper.HeaderRecord{
				MaxChannels: 16,
			},
			expectedRecord: &helper.TraceBuffer{
				BufferRawBytes: []byte{
					0x00, 0x01, // Timestamp: 1
					0xFF, 0xFF, 0xFF, // Red status
					0xFF, 0xFF, 0xFF, // Yellow status
					0xFF, 0xFF, 0xFF, // Green status
					0x01, // EE_SF_RE
					0x80, // AC Voltage
				},
				Timestamp: 1,
				Reds:      []bool{true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true},
				Yellows:   []bool{true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true},
				Greens:    []bool{true, true, true, true, true, true, true, true, true, true, true, true, true, true, true, true},
				EE_SF_RE:  true,
				AcVoltage: 128,
			},
		},
		{
			name: "valid trace record with no channels enabled",
			traceBytes: []byte{
				0x00, 0x02, // Timestamp: 2
				0x00, 0x00, 0x00, // Red status: no channels enabled
				0x00, 0x00, 0x00, // Yellow status: no channels enabled
				0x00, 0x00, 0x00, // Green status: no channels enabled
				0x00, // EE_SF_RE: false
				0x00, // AC Voltage: 0
			},
			header: &helper.HeaderRecord{
				MaxChannels: 16,
			},
			expectedRecord: &helper.TraceBuffer{
				BufferRawBytes: []byte{
					0x00, 0x02, // Timestamp: 2
					0x00, 0x00, 0x00, // Red status
					0x00, 0x00, 0x00, // Yellow status
					0x00, 0x00, 0x00, // Green status
					0x00, // EE_SF_RE
					0x00, // AC Voltage
				},
				Timestamp: 2,
				Reds:      []bool{false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false},
				Yellows:   []bool{false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false},
				Greens:    []bool{false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false},
				EE_SF_RE:  false,
				AcVoltage: 0,
			},
		},
		{
			name: "valid trace record with mixed channel status",
			traceBytes: []byte{
				0x00, 0x03, // Timestamp: 3
				0x01, 0x00, 0x00, // Red status: only channel 1 enabled
				0x00, 0x01, 0x00, // Yellow status: only channel 9 enabled
				0x00, 0x00, 0x01, // Green status: only channel 17 enabled
				0x01, // EE_SF_RE: true
				0x40, // AC Voltage: 64
			},
			header: &helper.HeaderRecord{
				MaxChannels: 16,
			},
			expectedRecord: &helper.TraceBuffer{
				BufferRawBytes: []byte{
					0x00, 0x03, // Timestamp: 3
					0x01, 0x00, 0x00, // Red status
					0x00, 0x01, 0x00, // Yellow status
					0x00, 0x00, 0x01, // Green status
					0x01, // EE_SF_RE
					0x40, // AC Voltage
				},
				Timestamp: 3,
				Reds:      []bool{false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false},
				Yellows:   []bool{false, false, false, false, false, false, false, false, true, false, false, false, false, false, false, false},
				Greens:    []bool{true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false},
				EE_SF_RE:  true,
				AcVoltage: 64,
			},
		},
		{
			name: "valid trace record with minimum channels",
			traceBytes: []byte{
				0x00, 0x04, // Timestamp: 4
				0x01, 0x00, 0x00, // Red status: only channel 1 enabled
				0x00, 0x00, 0x00, // Yellow status: no channels enabled
				0x00, 0x00, 0x00, // Green status: no channels enabled
				0x00, // EE_SF_RE: false
				0x00, // AC Voltage: 0
			},
			header: &helper.HeaderRecord{
				MaxChannels: 1,
			},
			expectedRecord: &helper.TraceBuffer{
				BufferRawBytes: []byte{
					0x00, 0x04, // Timestamp: 4
					0x01, 0x00, 0x00, // Red status
					0x00, 0x00, 0x00, // Yellow status
					0x00, 0x00, 0x00, // Green status
					0x00, // EE_SF_RE
					0x00, // AC Voltage
				},
				Timestamp: 4,
				Reds:      []bool{false},
				Yellows:   []bool{false},
				Greens:    []bool{false},
				EE_SF_RE:  false,
				AcVoltage: 0,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			record := parseTraceRecord(tt.traceBytes, tt.header)
			assert.NotNil(t, record)
			assert.Equal(t, tt.expectedRecord, record)
		})
	}
}

func TestGetFaultTextKCL2018(t *testing.T) {
	tests := []struct {
		name         string
		faultCode    int
		expectedText string
	}{
		{
			name:         "VDC Fail",
			faultCode:    1,
			expectedText: "+24VDC Low Fault (VDC Fail)",
		},
		{
			name:         "WDT Error",
			faultCode:    2,
			expectedText: "CU Watchdog Fault (WDT Error)",
		},
		{
			name:         "Conflict Fault",
			faultCode:    3,
			expectedText: "Conflict Fault",
		},
		{
			name:         "Dual Indication Fault",
			faultCode:    4,
			expectedText: "Dual Indication Fault",
		},
		{
			name:         "Red Fail Fault",
			faultCode:    5,
			expectedText: "Red Fail Fault",
		},
		{
			name:         "Clearance Skipped Yellow Fault",
			faultCode:    6,
			expectedText: "Clearance (Skipped Yellow) Fault",
		},
		{
			name:         "BND Fault",
			faultCode:    7,
			expectedText: "BND Fault",
		},
		{
			name:         "Diagnostic Fault",
			faultCode:    8,
			expectedText: "Diagnostic Fault",
		},
		{
			name:         "Program Card Ajar Fault",
			faultCode:    9,
			expectedText: "Program Card Ajar Fault",
		},
		{
			name:         "AC Line Low Voltage",
			faultCode:    10,
			expectedText: "AC Line Low Voltage",
		},
		{
			name:         "Red Cable Fault",
			faultCode:    12,
			expectedText: "Red Cable Fault",
		},
		{
			name:         "Configuration Change Fault",
			faultCode:    13,
			expectedText: "Configuration Change Fault",
		},
		{
			name:         "Clearance Short Yellow Fault",
			faultCode:    14,
			expectedText: "Clearance (Short Yellow) Fault",
		},
		{
			name:         "Recurrent Pulse Conflict",
			faultCode:    15,
			expectedText: "Recurrent Pulse Conflict",
		},
		{
			name:         "Recurrent Pulse Dual Indication",
			faultCode:    16,
			expectedText: "Recurrent Pulse Dual Indication",
		},
		{
			name:         "Recurrent Pulse Red Fail",
			faultCode:    17,
			expectedText: "Recurrent Pulse Red Fail",
		},
		{
			name:         "48VDC Fault",
			faultCode:    18,
			expectedText: "+48VDC Fault",
		},
		{
			name:         "Data Key Absent",
			faultCode:    19,
			expectedText: "Data Key Absent",
		},
		{
			name:         "Data Key FCS Error",
			faultCode:    20,
			expectedText: "Data Key FCS Error",
		},
		{
			name:         "Data Key Invalid Parameter Error",
			faultCode:    21,
			expectedText: "Data Key Invalid Parameter Error",
		},
		{
			name:         "Minimum Yellow + Red Clearance Fault",
			faultCode:    22,
			expectedText: "Minimum Yellow + Red Clearance Fault",
		},
		{
			name:         "24VDC High Fault",
			faultCode:    23,
			expectedText: "+24VDC High Faul",
		},
		{
			name:         "24VDC Ripple Fault",
			faultCode:    24,
			expectedText: "+24VDC Ripple Fault",
		},
		{
			name:         "Incorrect Program Card Fault",
			faultCode:    25,
			expectedText: "Incorrect Program Card Fault",
		},
		{
			name:         "AC Line Frequency Fault",
			faultCode:    26,
			expectedText: "",
		},
		{
			name:         "FYA Flash Rate Fault",
			faultCode:    27,
			expectedText: "FYA Flash Rate Fault",
		},
		{
			name:         "Unknown Fault Code",
			faultCode:    999,
			expectedText: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getFaultTextKCL2018(tt.faultCode)
			assert.Equal(t, tt.expectedText, result)
		})
	}
}

func TestLogFaultSignalSequence(t *testing.T) {
	tests := []struct {
		name           string
		byteMsg        []byte
		httpHeader     *pubsubdata.HeaderDetails
		header         *helper.HeaderRecord
		expectedError  error
		validateRecord func(t *testing.T, records *helper.FaultSignalSequenceRecords)
	}{
		{
			name: "valid fault signal sequence with single record",
			byteMsg: func() []byte {
				msgLen := HeaderLength + FaultTypeLength + Trace2018Length + ChecksumLength
				msg := make([]byte, msgLen)
				// Header
				msg[0] = 0x01 // Message type
				msg[1] = 0x02 // Device address
				msg[2] = 0x03 // Command code
				msg[3] = 0x00 // Response status
				msg[7] = 0x01 // Number of records

				// Fault type and number of trace records
				msg[HeaderLength] = 0x01                 // Fault type: VDC Fail
				msg[HeaderLength+FaultTypeLength] = 0x01 // Number of trace records

				// Trace record (starts at HeaderLength+FaultTypeLength+1)
				base := HeaderLength + FaultTypeLength + 1
				msg[base+0] = 0x00  // Timestamp MSB
				msg[base+1] = 0x01  // Timestamp LSB
				msg[base+2] = 0x01  // Red status byte 1 (channel 1 enabled)
				msg[base+3] = 0x00  // Red status byte 2
				msg[base+4] = 0x00  // Red status byte 3
				msg[base+5] = 0x00  // Yellow status byte 1
				msg[base+6] = 0x00  // Yellow status byte 2
				msg[base+7] = 0x00  // Yellow status byte 3
				msg[base+8] = 0x00  // Green status byte 1
				msg[base+9] = 0x00  // Green status byte 2
				msg[base+10] = 0x00 // Green status byte 3
				msg[base+11] = 0x00 // EE_SF_RE
				msg[base+12] = 0xF4 // AC Voltage: 244

				// Calculate checksum
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum // One's complement of sum

				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				MaxChannels: 16,
			},
			expectedError: nil,
			validateRecord: func(t *testing.T, records *helper.FaultSignalSequenceRecords) {
				require.NotNil(t, records)
				assert.Equal(t, DeviceModel, records.DeviceModel)
				assert.Equal(t, "+24VDC Low Fault (VDC Fail)", records.FaultType)
				require.Len(t, records.Records, 1)
				record := records.Records[0]
				assert.Equal(t, int64(0), record.Timestamp)
				assert.Equal(t, make([]bool, 16), record.Reds)
				assert.Equal(t, make([]bool, 16), record.Yellows)
				assert.Equal(t, make([]bool, 16), record.Greens)
				assert.False(t, record.EE_SF_RE)
				assert.Equal(t, 244, record.AcVoltage)
			},
		},
		{
			name: "nil header",
			byteMsg: func() []byte {
				msg := make([]byte, HeaderLength+FaultTypeLength+1+Trace2018Length+ChecksumLength)
				// Set minimal valid message
				msg[0] = 0x01                            // Message type
				msg[1] = 0x02                            // Device address
				msg[2] = 0x03                            // Command code
				msg[3] = 0x00                            // Response status
				msg[7] = 0x01                            // Number of records
				msg[HeaderLength] = 0x01                 // Fault type
				msg[HeaderLength+FaultTypeLength] = 0x01 // Number of trace records
				// Calculate checksum
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header:        nil,
			expectedError: helper.ErrMsgHeaderRecordNil,
		},
		{
			name: "invalid checksum",
			byteMsg: func() []byte {
				msg := make([]byte, HeaderLength+FaultTypeLength+1+Trace2018Length+ChecksumLength)
				// Set minimal valid message
				msg[0] = 0x01                            // Message type
				msg[1] = 0x02                            // Device address
				msg[2] = 0x03                            // Command code
				msg[3] = 0x00                            // Response status
				msg[7] = 0x01                            // Number of records
				msg[HeaderLength] = 0x01                 // Fault type
				msg[HeaderLength+FaultTypeLength] = 0x01 // Number of trace records
				msg[len(msg)-1] = 0xFF                   // Invalid checksum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				MaxChannels: 16,
			},
			expectedError: helper.ErrMsgByteChecksum,
		},
		{
			name: "unsupported device model",
			byteMsg: func() []byte {
				msgLen := HeaderLength + FaultTypeLength + Trace2018Length + ChecksumLength
				msg := make([]byte, msgLen)
				// Set minimal valid message
				msg[0] = 0x01                            // Message type
				msg[1] = 0x02                            // Device address
				msg[2] = 0x03                            // Command code
				msg[3] = 0x00                            // Response status
				msg[7] = 0x01                            // Number of records
				msg[HeaderLength] = 0x01                 // Fault type
				msg[HeaderLength+FaultTypeLength] = 0x01 // Number of trace records
				// Calculate checksum
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:            9999, // not helper.Ecl2010
				FirmwareRevision: 0x51, // > 0x50 to trigger error
				MaxChannels:      16,
			},
			expectedError: helper.ErrUnsupportedDevice,
			validateRecord: func(t *testing.T, records *helper.FaultSignalSequenceRecords) {
				assert.Nil(t, records)
			},
		},
		{
			name: "invalid message length",
			byteMsg: func() []byte {
				msgLen := HeaderLength + FaultTypeLength + Trace2018Length + ChecksumLength - 1 // One byte too short
				msg := make([]byte, msgLen)
				// Set minimal valid message
				msg[0] = 0x01                            // Message type
				msg[1] = 0x02                            // Device address
				msg[2] = 0x03                            // Command code
				msg[3] = 0x00                            // Response status
				msg[7] = 0x01                            // Number of records
				msg[HeaderLength] = 0x01                 // Fault type
				msg[HeaderLength+FaultTypeLength] = 0x01 // Number of trace records
				// Calculate checksum
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				MaxChannels: 16,
			},
			expectedError: helper.ErrMsgByteLen,
		},
		{
			name: "valid fault signal sequence with multiple records",
			byteMsg: func() []byte {
				msgLen := HeaderLength + FaultTypeLength + 2*Trace2018Length + ChecksumLength
				msg := make([]byte, msgLen)
				// Header
				msg[0] = 0x01 // Message type
				msg[1] = 0x02 // Device address
				msg[2] = 0x03 // Command code
				msg[3] = 0x00 // Response status
				msg[7] = 0x02 // Number of records

				// Fault type and number of trace records
				msg[HeaderLength] = 0x01                 // Fault type: VDC Fail
				msg[HeaderLength+FaultTypeLength] = 0x02 // Number of trace records

				// First trace record
				base1 := HeaderLength + FaultTypeLength + 1
				msg[base1+0] = 0x00  // Timestamp MSB
				msg[base1+1] = 0x01  // Timestamp LSB
				msg[base1+2] = 0x01  // Red status byte 1 (channel 1 enabled)
				msg[base1+3] = 0x00  // Red status byte 2
				msg[base1+4] = 0x00  // Red status byte 3
				msg[base1+5] = 0x00  // Yellow status byte 1
				msg[base1+6] = 0x00  // Yellow status byte 2
				msg[base1+7] = 0x00  // Yellow status byte 3
				msg[base1+8] = 0x00  // Green status byte 1
				msg[base1+9] = 0x00  // Green status byte 2
				msg[base1+10] = 0x00 // Green status byte 3
				msg[base1+11] = 0x00 // EE_SF_RE
				msg[base1+12] = 0xF4 // AC Voltage: 244

				// Second trace record
				base2 := base1 + Trace2018Length
				msg[base2+0] = 0x00  // Timestamp MSB
				msg[base2+1] = 0x02  // Timestamp LSB
				msg[base2+2] = 0x02  // Red status byte 1 (channel 2 enabled)
				msg[base2+3] = 0x00  // Red status byte 2
				msg[base2+4] = 0x00  // Red status byte 3
				msg[base2+5] = 0x00  // Yellow status byte 1
				msg[base2+6] = 0x00  // Yellow status byte 2
				msg[base2+7] = 0x00  // Yellow status byte 3
				msg[base2+8] = 0x00  // Green status byte 1
				msg[base2+9] = 0x00  // Green status byte 2
				msg[base2+10] = 0x00 // Green status byte 3
				msg[base2+11] = 0x01 // EE_SF_RE
				msg[base2+12] = 0x6D // AC Voltage: 109

				// Calculate checksum
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum // One's complement of sum

				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				MaxChannels: 16,
			},
			expectedError: nil,
			validateRecord: func(t *testing.T, records *helper.FaultSignalSequenceRecords) {
				assert.NotNil(t, records)
				assert.Equal(t, DeviceModel, records.DeviceModel)
				assert.Equal(t, "+24VDC Low Fault (VDC Fail)", records.FaultType)
				require.Len(t, records.Records, 2)

				// First record
				record1 := records.Records[0]
				assert.Equal(t, int64(0), record1.Timestamp)
				assert.Equal(t, make([]bool, 16), record1.Reds)
				assert.Equal(t, make([]bool, 16), record1.Yellows)
				assert.Equal(t, make([]bool, 16), record1.Greens)
				assert.False(t, record1.EE_SF_RE)
				assert.Equal(t, 244, record1.AcVoltage)

				// Second record
				record2 := records.Records[1]
				assert.Equal(t, int64(5), record2.Timestamp)
				assert.Equal(t, make([]bool, 16), record2.Reds)
				assert.Equal(t, make([]bool, 16), record2.Yellows)
				assert.Equal(t, make([]bool, 16), record2.Greens)
				assert.True(t, record2.EE_SF_RE)
				assert.Equal(t, 249, record2.AcVoltage)
			},
		},
		{
			name: "timestamp exceeds max value",
			byteMsg: func() []byte {
				msgLen := HeaderLength + FaultTypeLength + Trace2018Length + ChecksumLength
				msg := make([]byte, msgLen)
				// Header
				msg[0] = 0x01 // Message type
				msg[1] = 0x02 // Device address
				msg[2] = 0x03 // Command code
				msg[3] = 0x00 // Response status
				msg[7] = 0x01 // Number of records

				// Fault type and number of trace records
				msg[HeaderLength] = 0x01                 // Fault type: VDC Fail
				msg[HeaderLength+FaultTypeLength] = 0x01 // Number of trace records

				// Trace record with timestamp exceeding max value
				base := HeaderLength + FaultTypeLength + 1
				msg[base+0] = 0xFF  // Timestamp MSB
				msg[base+1] = 0xFF  // Timestamp LSB (65535)
				msg[base+2] = 0x01  // Red status byte 1 (channel 1 enabled)
				msg[base+3] = 0x00  // Red status byte 2
				msg[base+4] = 0x00  // Red status byte 3
				msg[base+5] = 0x00  // Yellow status byte 1
				msg[base+6] = 0x00  // Yellow status byte 2
				msg[base+7] = 0x00  // Yellow status byte 3
				msg[base+8] = 0x00  // Green status byte 1
				msg[base+9] = 0x00  // Green status byte 2
				msg[base+10] = 0x00 // Green status byte 3
				msg[base+11] = 0x00 // EE_SF_RE
				msg[base+12] = 0xF4 // AC Voltage: 244

				// Calculate checksum
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum // One's complement of sum

				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				MaxChannels: 16,
			},
			expectedError: nil,
			validateRecord: func(t *testing.T, records *helper.FaultSignalSequenceRecords) {
				assert.NotNil(t, records)
				assert.Equal(t, DeviceModel, records.DeviceModel)
				assert.Equal(t, "+24VDC Low Fault (VDC Fail)", records.FaultType)
				require.Len(t, records.Records, 1)
				record := records.Records[0]
				assert.Equal(t, int64(0), record.Timestamp) // Should be capped at max value
				assert.Equal(t, make([]bool, 16), record.Reds)
				assert.Equal(t, make([]bool, 16), record.Yellows)
				assert.Equal(t, make([]bool, 16), record.Greens)
				assert.False(t, record.EE_SF_RE)
				assert.Equal(t, 247, record.AcVoltage)
			},
		},
		{
			name: "error parsing trace record",
			byteMsg: func() []byte {
				msgLen := HeaderLength + FaultTypeLength + 5 + ChecksumLength // 5 bytes for trace record instead of 13
				msg := make([]byte, msgLen)
				// Header
				msg[0] = 0x01 // Message type
				msg[1] = 0x02 // Device address
				msg[2] = 0x03 // Command code
				msg[3] = 0x00 // Response status
				msg[7] = 0x01 // Number of records

				// Fault type and number of trace records
				msg[HeaderLength] = 0x01                 // Fault type: VDC Fail
				msg[HeaderLength+FaultTypeLength] = 0x01 // Number of trace records

				// Invalid trace record (only 5 bytes instead of 13)
				base := HeaderLength + FaultTypeLength + 1
				copy(msg[base:], []byte{0x00, 0x01, 0x01, 0x00, 0x00})

				// Calculate checksum
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum // One's complement of sum

				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				MaxChannels: 16,
			},
			expectedError: helper.ErrMsgByteLen,
			validateRecord: func(t *testing.T, records *helper.FaultSignalSequenceRecords) {
				assert.Nil(t, records)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			device := EDIECL2010{}
			records, err := device.LogFaultSignalSequence(tt.httpHeader, tt.byteMsg, tt.header)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedError)
				if tt.validateRecord != nil {
					tt.validateRecord(t, records)
				}
			} else {
				assert.NoError(t, err)
				if tt.validateRecord != nil {
					tt.validateRecord(t, records)
				}
			}
		})
	}
}
