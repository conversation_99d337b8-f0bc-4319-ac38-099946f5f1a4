package ediecl2010

import (
	"fmt"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

const (
	Trace2018Length   = 13
	FaultTypeLength   = 1
	TraceMaxTimestamp = int64(65530)
)

/*
Byte Message Layout
==================

Header (8 bytes)
---------------
[0] Message type identifier
[1] Device address
[2] Command code
[3] Response status
[4] Reserved byte
[5] Reserved byte
[6] Reserved byte
[7] Reserved byte

FaultSignalSequence information (2 bytes)
---------------------------------------
[8] Fault type
[9] Number of trace records

Trace Records (13 bytes per record)
----------------------------------
[10] Timestamp (MSB)
[11] Timestamp (LSB)
[12-14] Red status (big endian)
[15-17] Yellow status (big endian)
[18-20] Green status (big endian)
[21] EE_SF_RE
[22] AcVoltage

Checksum (1 byte)
----------------
[Last byte] Checksum
*/

// LogFaultSignalSequence parses the fault signal sequence log bytes into a FaultSignalSequenceRecords struct
func (device EDIECL2010) LogFaultSignalSequence(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (allRecords *helper.FaultSignalSequenceRecords, err error) {
	if header == nil {
		return nil, fmt.Errorf("%w header is nil", helper.ErrMsgHeaderRecordNil)
	}

	if err = helper.ValidateChecksum(byteMsg); err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	if err = validateDeviceInfo(header); err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrUnsupportedDevice, err)
	}

	faultType := int(byteMsg[HeaderLength])
	numTraceRecords := int(byteMsg[HeaderLength+FaultTypeLength])
	byteMessageLength := HeaderLength + FaultTypeLength + numTraceRecords*Trace2018Length + ChecksumLength
	traceOffset := HeaderLength + FaultTypeLength + 1
	allRecords = &helper.FaultSignalSequenceRecords{
		DeviceModel: DeviceModel,
		RawMessage:  byteMsg,
		FaultType:   getFaultTextKCL2018(faultType),
	}

	if !helper.VerifyByteLen(byteMsg, byteMessageLength) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), byteMessageLength)
	}

	for idx := range numTraceRecords {
		traceBytes := byteMsg[traceOffset+idx*Trace2018Length : traceOffset+(idx+1)*Trace2018Length]
		record := parseTraceRecord(traceBytes, header)
		if record.Timestamp > TraceMaxTimestamp {
			record.Timestamp = TraceMaxTimestamp
		}
		allRecords.Records = append(allRecords.Records, *record)
	}

	// adjust time stamps
	helper.NormalizeTimestamps(allRecords) // double-check this to make sure the data is modified in the structure
	allRecords = helper.Performcalcs(allRecords)

	return allRecords, nil
}

func parseTraceRecord(traceBytes []byte, header *helper.HeaderRecord) (record *helper.TraceBuffer) {
	record = &helper.TraceBuffer{
		BufferRawBytes: traceBytes,
		Timestamp:      int64(traceBytes[0])<<8 | int64(traceBytes[1]),
		Reds:           parseChannelStatus(combineBytes(traceBytes[2:5]), int(header.MaxChannels)),
		Yellows:        parseChannelStatus(combineBytes(traceBytes[5:8]), int(header.MaxChannels)),
		Greens:         parseChannelStatus(combineBytes(traceBytes[8:11]), int(header.MaxChannels)),
		EE_SF_RE:       traceBytes[11]&0x1 == 1,
		AcVoltage:      int(traceBytes[12]),
	}
	return record
}

func getFaultTextKCL2018(faultCode int) string {
	var faultText string

	switch faultCode {
	case 1:
		faultText = "+24VDC Low Fault (VDC Fail)"
	case 2:
		faultText = "CU Watchdog Fault (WDT Error)"
	case 3:
		faultText = "Conflict Fault"
	case 4:
		faultText = "Dual Indication Fault"
	case 5:
		faultText = "Red Fail Fault"
	case 6:
		faultText = "Clearance (Skipped Yellow) Fault"
	case 7:
		faultText = "BND Fault"
	case 8:
		faultText = "Diagnostic Fault"
	case 9:
		faultText = "Program Card Ajar Fault"
	case 10:
		faultText = "AC Line Low Voltage"
	case 12:
		faultText = "Red Cable Fault"
	case 13:
		faultText = "Configuration Change Fault"
	case 14:
		faultText = "Clearance (Short Yellow) Fault"
	case 15:
		faultText = "Recurrent Pulse Conflict"
	case 16:
		faultText = "Recurrent Pulse Dual Indication"
	case 17:
		faultText = "Recurrent Pulse Red Fail"
	case 18:
		faultText = "+48VDC Fault"
	case 19:
		faultText = "Data Key Absent"
	case 20:
		faultText = "Data Key FCS Error"
	case 21:
		faultText = "Data Key Invalid Parameter Error"
	case 22:
		faultText = "Minimum Yellow + Red Clearance Fault"
	case 23:
		faultText = "+24VDC High Faul"
	case 24:
		faultText = "+24VDC Ripple Fault"
	case 25:
		faultText = "Incorrect Program Card Fault"
	// case 26:
	//	faultText = "AC Line Frequency Fault"
	case 27:
		faultText = "FYA Flash Rate Fault"
	default:
		faultText = ""
	}

	return faultText
}
