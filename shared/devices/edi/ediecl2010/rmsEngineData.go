package ediecl2010

import (
	"fmt"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

const (
	RMSEngineDataLength     = 10
	RMSEngineRevisionOffset = 8
)

/*
Byte Message Format
===================

RMS Engine Data (10 bytes)
--------------------------
[0-7] Reserved bytes
[8] Engine revision
[9] Checksum
*/

// RMSEngineData parses the rms engine data from the byte message
func (device EDIECL2010) RMSEngineData(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (rmsEngineDetail *helper.RmsEngineRecord, err error) {
	if header == nil {
		return nil, fmt.Errorf("%w header is nil", helper.ErrMsgHeaderRecordNil)
	}

	if err = helper.ValidateChecksum(byteMsg); err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	if header.CommVersion < 0x27 {
		return nil, nil
	}

	if header.Model == helper.Nsm3e {
		return nil, nil
	}

	rmsEngineDetail = &helper.RmsEngineRecord{
		DeviceModel:    DeviceModel,
		EngineRevision: int64(byteMsg[RMSEngineRevisionOffset]),
	}

	return rmsEngineDetail, nil
}
