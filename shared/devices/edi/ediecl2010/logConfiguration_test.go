package ediecl2010

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func buildValidConfigLogMsg() []byte {
	msg := make([]byte, HeaderLength+CF2018LogLength+ChecksumLength)

	// Header
	msg[0] = 0x01 // Message type
	msg[1] = 0x02 // Device address
	msg[2] = 0x03 // Command code
	msg[3] = 0x00 // Response status
	msg[7] = 0x01 // Number of records

	// Conflict map (54 bytes - 18 entries * 3 bytes each, offsets 8–61)
	for i := 8; i < 62; i++ {
		msg[i] = 0x00
	}

	// Enable flags (offsets after conflict map)
	// Red Fail Enable (3 bytes)
	msg[59] = 0x00
	msg[60] = 0x00
	msg[61] = 0x00

	// Green Yellow Dual Enable (3 bytes)
	msg[62] = 0x00
	msg[63] = 0x00
	msg[64] = 0x00

	// Yellow Red Dual Enable (3 bytes)
	msg[65] = 0x00
	msg[66] = 0x00
	msg[67] = 0x00

	// Green Red Dual Enable (3 bytes)
	msg[68] = 0x00
	msg[69] = 0x00
	msg[70] = 0x00

	// Minimum Yellow Clearance Enable (3 bytes)
	msg[71] = 0x00
	msg[72] = 0x00
	msg[73] = 0x00

	// Reserved bytes (74-76)
	msg[74] = 0x00
	msg[75] = 0x00
	msg[76] = 0x00

	// Yellow Disable (3 bytes)
	msg[77] = 0x00
	msg[78] = 0x00
	msg[79] = 0x00

	// Reserved bytes (80-84)
	msg[80] = 0x00
	msg[81] = 0x00
	msg[82] = 0x00
	msg[83] = 0x00
	msg[84] = 0x00

	// Date-time (2024-03-15 14:30:00)
	msg[85] = 0x00 // Seconds
	msg[86] = 0x30 // Minutes
	msg[87] = 0x14 // Hours
	msg[88] = 0x15 // Day
	msg[89] = 0x03 // Month
	msg[90] = 0x24 // Year

	// Reserved bytes (91-92)
	msg[91] = 0x00
	msg[92] = 0x00

	// Calculate checksum
	var sum byte
	for i := 0; i < len(msg)-1; i++ {
		sum += msg[i]
	}
	msg[len(msg)-1] = ^sum // One's complement of sum

	return msg
}

func TestLogConfiguration(t *testing.T) {
	tests := []struct {
		name          string
		byteMsg       []byte
		header        *helper.HeaderRecord
		httpHeader    *pubsubdata.HeaderDetails
		expectedError error
		validate      func(t *testing.T, records *helper.ConfigurationChangeLogRecords)
	}{
		{
			name:       "valid configuration change log",
			byteMsg:    buildValidConfigLogMsg(),
			header:     createValidConfigHeader(false),
			httpHeader: createValidConfigHTTPHeader(),
			validate: func(t *testing.T, records *helper.ConfigurationChangeLogRecords) {
				require.NotNil(t, records)
				assert.Equal(t, DeviceModel, records.DeviceModel)
				assert.Equal(t, buildValidConfigLogMsg(), records.RawMessage)
				require.Len(t, records.Record, 1)
			},
		},
		{
			name:          "nil header",
			byteMsg:       buildValidConfigLogMsg(),
			header:        nil,
			httpHeader:    createValidConfigHTTPHeader(),
			expectedError: helper.ErrMsgHeaderRecordNil,
		},
		{
			name:          "invalid checksum",
			byteMsg:       createInvalidConfigChecksumMsg(),
			header:        createValidConfigHeader(false),
			httpHeader:    createValidConfigHTTPHeader(),
			expectedError: helper.ErrMsgByteChecksum,
		},
		{
			name:          "unsupported device model",
			byteMsg:       buildValidConfigLogMsg(),
			header:        createUnsupportedDeviceHeader(),
			httpHeader:    createValidConfigHTTPHeader(),
			expectedError: helper.ErrUnsupportedDevice,
		},
		{
			name: "invalid message length (wrong length, valid checksum)",
			byteMsg: func() []byte {
				msg := buildValidConfigLogMsg()
				// Remove one byte to make the length invalid
				msg = msg[:len(msg)-2]
				// Recalculate checksum for new length
				var sum byte
				for _, b := range msg[:len(msg)-1] {
					sum += b
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			header:        createValidConfigHeader(false),
			httpHeader:    createValidConfigHTTPHeader(),
			expectedError: helper.ErrMsgByteLen,
		},
		{
			name: "invalid BCD date-time in record",
			byteMsg: func() []byte {
				msg := buildValidConfigLogMsg()
				// Set invalid BCD values for date-time
				msg[85] = 0xFF // Invalid seconds
				msg[86] = 0xFF // Invalid minutes
				msg[87] = 0xFF // Invalid hours
				msg[88] = 0xFF // Invalid day
				msg[89] = 0xFF // Invalid month
				msg[90] = 0xFF // Invalid year
				// Recalculate checksum
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			header:        createValidConfigHeader(false),
			httpHeader:    createValidConfigHTTPHeader(),
			expectedError: fmt.Errorf("failed to parse configuration change log record 0: bad month - month reported = 255 invalid BCD byte : 0xFF (nibbles 15,15)"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			device := EDIECL2010{}
			records, err := device.LogConfiguration(tt.httpHeader, tt.byteMsg, tt.header)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.ErrorContains(t, err, tt.expectedError.Error())
				assert.Nil(t, records)
			} else {
				assert.NoError(t, err)
				if tt.validate != nil && records != nil {
					tt.validate(t, records)
				}
			}
		})
	}
}

// Helper functions to create test data
func createValidConfigHeader(mainsDC bool) *helper.HeaderRecord {
	return &helper.HeaderRecord{
		Model:          helper.Ecl2010,
		MainsDC:        mainsDC,
		PowerDownLevel: 100,
	}
}

func createValidConfigHTTPHeader() *pubsubdata.HeaderDetails {
	return &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}
}

func createUnsupportedDeviceHeader() *helper.HeaderRecord {
	return &helper.HeaderRecord{
		Model:            9999, // not helper.Ecl2010
		FirmwareRevision: 0x51, // > 0x50
	}
}

func createInvalidConfigChecksumMsg() []byte {
	msg := buildValidConfigLogMsg()
	msg[len(msg)-1] = 0xFF // Invalid checksum
	return msg
}

func TestParseEnableFlags(t *testing.T) {
	tests := []struct {
		name     string
		value    int
		count    int
		expected []bool
	}{
		{
			name:     "all flags disabled",
			value:    0,
			count:    4,
			expected: []bool{false, false, false, false},
		},
		{
			name:     "all flags enabled",
			value:    15, // 1111 in binary
			count:    4,
			expected: []bool{true, true, true, true},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := parseEnableFlags(tt.value, tt.count)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetIntFromLhMsHs(t *testing.T) {
	tests := []struct {
		name     string
		lh       byte
		ms       byte
		hs       byte
		expected int
	}{
		{
			name:     "zero value",
			lh:       0x00,
			ms:       0x00,
			hs:       0x00,
			expected: 0,
		},
		{
			name:     "maximum value",
			lh:       0xFF,
			ms:       0xFF,
			hs:       0xFF,
			expected: 0xFFFFFF,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getIntFromLhMsHs(tt.lh, tt.ms, tt.hs)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestParseConflictMap(t *testing.T) {
	tests := []struct {
		name     string
		input    []byte
		expected [18]int
	}{
		{
			name: "all zeros",
			input: func() []byte {
				bytes := make([]byte, 54) // 18 entries * 3 bytes each
				return bytes
			}(),
			expected: [18]int{},
		},
		{
			name: "single entry",
			input: func() []byte {
				bytes := make([]byte, 54)
				bytes[0] = 0x01 // Low byte
				bytes[1] = 0x00 // Middle byte
				bytes[2] = 0x00 // High byte
				return bytes
			}(),
			expected: func() [18]int {
				var result [18]int
				result[0] = 65536
				return result
			}(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := parseConflictMap(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestParseOptions1(t *testing.T) {
	tests := []struct {
		name           string
		options1       byte
		param          byte
		expectedRecord *helper.ConfigurationChangeLogRecord
	}{
		{
			name:     "all options disabled",
			options1: 0x00,
			param:    0x00,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				RedFaultTiming:       "700-1000 ms",
				RecurrentPulse:       true,
				WatchdogTiming:       "1.5 seconds",
				WatchdogEnableSwitch: false,
				GYEnable:             false,
				LEDguardThresholds:   false,
				RedFailEnabledbySSM:  false,
			},
		},
		{
			name:     "all options enabled",
			options1: 0x6F, // 01101111
			param:    0x01,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				RedFaultTiming:       "1200-1500 ms",
				RecurrentPulse:       false,
				WatchdogTiming:       "1 second",
				WatchdogEnableSwitch: true,
				GYEnable:             true,
				LEDguardThresholds:   true,
				RedFailEnabledbySSM:  true,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			record := &helper.ConfigurationChangeLogRecord{}
			parseOptions1(tt.options1, tt.param, record)
			assert.Equal(t, tt.expectedRecord, record)
		})
	}
}

func TestParseFlashingYellowArrows(t *testing.T) {
	tests := []struct {
		name           string
		options1       byte
		options2       byte
		expectedRecord *helper.ConfigurationChangeLogRecord
	}{
		{
			name:     "no flashing arrows",
			options1: 0x00,
			options2: 0x00,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				FlashingYellowArrows: []string{"<none>"},
			},
		},
		{
			name:     "single flashing arrow - FYA mode",
			options1: 0x00,
			options2: 0x01,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				FlashingYellowArrows: []string{"1-9", "(Mode=FYA)"},
			},
		},
		{
			name:     "single flashing arrow - FYAC mode",
			options1: 0x80,
			options2: 0x01,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				FlashingYellowArrows: []string{"1-9", "(Mode=FYAC)"},
			},
		},
		{
			name:     "multiple flashing arrows - FYA mode",
			options1: 0x00,
			options2: 0x0F,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				FlashingYellowArrows: []string{"1-9", "3-10", "5-11", "7-12", "(Mode=FYA)"},
			},
		},
		{
			name:     "multiple flashing arrows - FYAC mode",
			options1: 0x80,
			options2: 0x0F,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				FlashingYellowArrows: []string{"1-9", "3-10", "5-11", "7-12", "(Mode=FYAC)"},
			},
		},
		{
			name:     "all flashing arrows - FYA mode",
			options1: 0x00,
			options2: 0xFF,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				FlashingYellowArrows: []string{"1-9", "3-10", "5-11", "7-12", "(Mode=FYA)"},
			},
		},
		{
			name:     "all flashing arrows - FYAC mode",
			options1: 0x80,
			options2: 0xFF,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				FlashingYellowArrows: []string{"1-9", "3-10", "5-11", "7-12", "(Mode=FYAC)"},
			},
		},
		{
			name:     "mixed flashing arrows - FYA mode",
			options1: 0x00,
			options2: 0x0A, // 1010 in binary - enables 3-10 and 7-12
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				FlashingYellowArrows: []string{"3-10", "7-12", "(Mode=FYA)"},
			},
		},
		{
			name:     "mixed flashing arrows - FYAC mode",
			options1: 0x80,
			options2: 0x0A, // 1010 in binary - enables 3-10 and 7-12
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				FlashingYellowArrows: []string{"3-10", "7-12", "(Mode=FYAC)"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			record := &helper.ConfigurationChangeLogRecord{}
			parseFlashingYellowArrows(tt.options1, tt.options2, record)
			assert.Equal(t, tt.expectedRecord, record)
		})
	}
}

func TestParseSelect1(t *testing.T) {
	tests := []struct {
		name           string
		select1        byte
		expectedRecord *helper.ConfigurationChangeLogRecord
	}{
		{
			name:    "all options disabled",
			select1: 0x00,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				WDTErrorClearonPU: true,
				MinimumFlash:      false,
				ConfigChangeFault: false,
				RedCableFault:     false,
				AcLineBrownout:    "92 +/- 2 Vrms",
			},
		},
		{
			name:    "all options enabled",
			select1: 0x1F, // 00011111 in binary
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				WDTErrorClearonPU: false,
				MinimumFlash:      true,
				ConfigChangeFault: true,
				RedCableFault:     true,
				AcLineBrownout:    "98 +/- 2Vrms",
			},
		},
		{
			name:    "only WDT error clear on PU enabled",
			select1: 0x01,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				WDTErrorClearonPU: false,
				MinimumFlash:      false,
				ConfigChangeFault: false,
				RedCableFault:     false,
				AcLineBrownout:    "92 +/- 2 Vrms",
			},
		},
		{
			name:    "only minimum flash enabled",
			select1: 0x02,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				WDTErrorClearonPU: true,
				MinimumFlash:      true,
				ConfigChangeFault: false,
				RedCableFault:     false,
				AcLineBrownout:    "92 +/- 2 Vrms",
			},
		},
		{
			name:    "only config change fault enabled",
			select1: 0x04,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				WDTErrorClearonPU: true,
				MinimumFlash:      false,
				ConfigChangeFault: true,
				RedCableFault:     false,
				AcLineBrownout:    "92 +/- 2 Vrms",
			},
		},
		{
			name:    "only red cable fault enabled",
			select1: 0x08,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				WDTErrorClearonPU: true,
				MinimumFlash:      false,
				ConfigChangeFault: false,
				RedCableFault:     true,
				AcLineBrownout:    "92 +/- 2 Vrms",
			},
		},
		{
			name:    "only AC line brownout enabled",
			select1: 0x10,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				WDTErrorClearonPU: true,
				MinimumFlash:      false,
				ConfigChangeFault: false,
				RedCableFault:     false,
				AcLineBrownout:    "98 +/- 2Vrms",
			},
		},
		{
			name:    "WDT error clear and minimum flash enabled",
			select1: 0x03,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				WDTErrorClearonPU: false,
				MinimumFlash:      true,
				ConfigChangeFault: false,
				RedCableFault:     false,
				AcLineBrownout:    "92 +/- 2 Vrms",
			},
		},
		{
			name:    "config change fault and red cable fault enabled",
			select1: 0x0C,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				WDTErrorClearonPU: true,
				MinimumFlash:      false,
				ConfigChangeFault: true,
				RedCableFault:     true,
				AcLineBrownout:    "92 +/- 2 Vrms",
			},
		},
		{
			name:    "all except AC line brownout enabled",
			select1: 0x0F,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				WDTErrorClearonPU: false,
				MinimumFlash:      true,
				ConfigChangeFault: true,
				RedCableFault:     true,
				AcLineBrownout:    "92 +/- 2 Vrms",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			record := &helper.ConfigurationChangeLogRecord{}
			parseSelect1(tt.select1, record)
			assert.Equal(t, tt.expectedRecord, record)
		})
	}
}

func TestParseSelect2(t *testing.T) {
	tests := []struct {
		name           string
		select2        byte
		expectedRecord *helper.ConfigurationChangeLogRecord
	}{
		{
			name:    "all options disabled",
			select2: 0x00,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				PinEEPolarity:             "NORMAL",
				DualIndicationFaultTiming: "350 - 500 ms",
				FYAFlashRateFault:         true,
			},
		},
		{
			name:    "all options enabled",
			select2: 0x43, // 01000011 in binary
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				PinEEPolarity:             "INVERT",
				DualIndicationFaultTiming: "700 - 1000 ms",
				FYAFlashRateFault:         false,
			},
		},
		{
			name:    "only pin EE polarity enabled",
			select2: 0x01,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				PinEEPolarity:             "INVERT",
				DualIndicationFaultTiming: "350 - 500 ms",
				FYAFlashRateFault:         true,
			},
		},
		{
			name:    "only dual indication fault timing enabled",
			select2: 0x02,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				PinEEPolarity:             "NORMAL",
				DualIndicationFaultTiming: "700 - 1000 ms",
				FYAFlashRateFault:         true,
			},
		},
		{
			name:    "only FYA flash rate fault enabled",
			select2: 0x40,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				PinEEPolarity:             "NORMAL",
				DualIndicationFaultTiming: "350 - 500 ms",
				FYAFlashRateFault:         false,
			},
		},
		{
			name:    "pin EE polarity and dual indication fault timing enabled",
			select2: 0x03,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				PinEEPolarity:             "INVERT",
				DualIndicationFaultTiming: "700 - 1000 ms",
				FYAFlashRateFault:         true,
			},
		},
		{
			name:    "pin EE polarity and FYA flash rate fault enabled",
			select2: 0x41,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				PinEEPolarity:             "INVERT",
				DualIndicationFaultTiming: "350 - 500 ms",
				FYAFlashRateFault:         false,
			},
		},
		{
			name:    "dual indication fault timing and FYA flash rate fault enabled",
			select2: 0x42,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				PinEEPolarity:             "NORMAL",
				DualIndicationFaultTiming: "700 - 1000 ms",
				FYAFlashRateFault:         false,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			record := &helper.ConfigurationChangeLogRecord{}
			parseSelect2(tt.select2, record)
			assert.Equal(t, tt.expectedRecord, record)
		})
	}
}

func TestParseConfigurationChangeLogRecord(t *testing.T) {
	tests := []struct {
		name           string
		configLogBytes []byte
		httpHeader     *pubsubdata.HeaderDetails
		header         *helper.HeaderRecord
		expectedError  string
	}{
		{
			name:           "valid configuration",
			configLogBytes: buildValidConfigLogMsg()[HeaderLength:],
			httpHeader:     createValidConfigHTTPHeader(),
			header:         createValidConfigHeader(false),
			expectedError:  "",
		},
		{
			name: "invalid BCD date-time",
			configLogBytes: func() []byte {
				msg := buildValidConfigLogMsg()[HeaderLength:]
				// Set invalid BCD values for date-time
				msg[81] = 0xFF // Invalid seconds
				msg[80] = 0xFF // Invalid minutes
				msg[82] = 0xFF // Invalid hours
				msg[79] = 0xFF // Invalid day
				msg[78] = 0xFF // Invalid month
				msg[77] = 0xFF // Invalid year
				return msg
			}(),
			httpHeader:    createValidConfigHTTPHeader(),
			header:        createValidConfigHeader(false),
			expectedError: "invalid BCD byte",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			record, err := parseConfigurationChangeLogRecord(tt.configLogBytes, tt.httpHeader, tt.header)
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.ErrorContains(t, err, tt.expectedError)
				assert.Nil(t, record)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, record)
			}
		})
	}
}

func TestParseChannelPermissives(t *testing.T) {
	tests := []struct {
		name           string
		conflictMap    [18]int
		maxChannels    int
		expectedRecord *helper.ConfigurationChangeLogRecord
	}{
		{
			name: "no permissives - all conflicts",
			conflictMap: func() [18]int {
				var cm [18]int
				// Set all bits to 1 to indicate conflicts
				for i := range cm {
					cm[i] = 0xFFFFFF
				}
				return cm
			}(),
			maxChannels: 4,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				Ch01Permissives: nil,
				Ch02Permissives: nil,
				Ch03Permissives: nil,
			},
		},
		{
			name: "all permissives - no conflicts",
			conflictMap: func() [18]int {
				var cm [18]int
				// All bits set to 0 to indicate no conflicts
				return cm
			}(),
			maxChannels: 4,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				Ch01Permissives: []string{"2", "3", "4"},
				Ch02Permissives: []string{"3", "4"},
				Ch03Permissives: []string{"4"},
			},
		},
		{
			name: "mixed permissives",
			conflictMap: func() [18]int {
				var cm [18]int
				// Channel 1 has conflict with 2 but not with 3,4
				cm[0] = 0x000002
				// Channel 2 has conflict with 3 but not with 4
				cm[1] = 0x000004
				// Channel 3 has no conflicts
				return cm
			}(),
			maxChannels: 4,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				Ch01Permissives: []string{"2", "4"},
				Ch02Permissives: []string{"3"},
				Ch03Permissives: []string{"4"},
			},
		},
		{
			name: "max channels edge case",
			conflictMap: func() [18]int {
				var cm [18]int
				return cm
			}(),
			maxChannels: 16,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				Ch01Permissives: []string{"2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16"},
				Ch02Permissives: []string{"3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16"},
				Ch03Permissives: []string{"4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16"},
				Ch04Permissives: []string{"5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16"},
				Ch05Permissives: []string{"6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16"},
				Ch06Permissives: []string{"7", "8", "9", "10", "11", "12", "13", "14", "15", "16"},
				Ch07Permissives: []string{"8", "9", "10", "11", "12", "13", "14", "15", "16"},
				Ch08Permissives: []string{"9", "10", "11", "12", "13", "14", "15", "16"},
				Ch09Permissives: []string{"10", "11", "12", "13", "14", "15", "16"},
				Ch10Permissives: []string{"11", "12", "13", "14", "15", "16"},
				Ch11Permissives: []string{"12", "13", "14", "15", "16"},
				Ch12Permissives: []string{"13", "14", "15", "16"},
				Ch13Permissives: []string{"14", "15", "16"},
				Ch14Permissives: []string{"15", "16"},
				Ch15Permissives: []string{"16"},
			},
		},
		{
			name: "single channel",
			conflictMap: func() [18]int {
				var cm [18]int
				return cm
			}(),
			maxChannels:    1,
			expectedRecord: &helper.ConfigurationChangeLogRecord{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			record := &helper.ConfigurationChangeLogRecord{}
			parseChannelPermissives(tt.conflictMap, tt.maxChannels, record)
			assert.Equal(t, tt.expectedRecord, record)
		})
	}
}
