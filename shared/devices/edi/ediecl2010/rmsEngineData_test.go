package ediecl2010

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func TestRMSEngineData(t *testing.T) {
	tests := []struct {
		name           string
		byteMsg        []byte
		httpHeader     *pubsubdata.HeaderDetails
		header         *helper.HeaderRecord
		expectedError  error
		validateRecord func(t *testing.T, rmsEngine *helper.RmsEngineRecord)
	}{
		{
			name: "nil header",
			byteMsg: func() []byte {
				msg := make([]byte, RMSEngineDataLength)
				msg[RMSEngineRevisionOffset] = 0x01 // Engine revision
				// Calculate checksum
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header:        nil,
			expectedError: helper.ErrMsgHeaderRecordNil,
		},
		{
			name: "invalid checksum",
			byteMsg: func() []byte {
				msg := make([]byte, RMSEngineDataLength)
				msg[RMSEngineRevisionOffset] = 0x01 // Engine revision
				msg[len(msg)-1] = 0xFF              // Invalid checksum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				CommVersion: 0x27,
			},
			expectedError: helper.ErrMsgByteChecksum,
		},
		{
			name: "comm version < 0x27",
			byteMsg: func() []byte {
				msg := make([]byte, RMSEngineDataLength)
				msg[RMSEngineRevisionOffset] = 0x01 // Engine revision
				// Calculate checksum
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				CommVersion: 0x26,
			},
			expectedError: nil,
			validateRecord: func(t *testing.T, rmsEngine *helper.RmsEngineRecord) {
				assert.Nil(t, rmsEngine)
			},
		},
		{
			name: "model is NSM3E",
			byteMsg: func() []byte {
				msg := make([]byte, RMSEngineDataLength)
				msg[RMSEngineRevisionOffset] = 0x01 // Engine revision
				// Calculate checksum
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:       helper.Nsm3e,
				CommVersion: 0x27,
			},
			expectedError: nil,
			validateRecord: func(t *testing.T, rmsEngine *helper.RmsEngineRecord) {
				assert.Nil(t, rmsEngine)
			},
		},
		{
			name: "valid message",
			byteMsg: func() []byte {
				msg := make([]byte, RMSEngineDataLength)
				msg[RMSEngineRevisionOffset] = 0x01 // Engine revision
				// Calculate checksum
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				CommVersion: 0x27,
			},
			expectedError: nil,
			validateRecord: func(t *testing.T, rmsEngine *helper.RmsEngineRecord) {
				assert.NotNil(t, rmsEngine)
				assert.Equal(t, DeviceModel, rmsEngine.DeviceModel)
				assert.Equal(t, int64(1), rmsEngine.EngineRevision)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			device := EDIECL2010{}
			rmsEngine, err := device.RMSEngineData(tt.httpHeader, tt.byteMsg, tt.header)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedError)
				assert.Nil(t, rmsEngine)
			} else {
				assert.NoError(t, err)
				if tt.validateRecord != nil {
					tt.validateRecord(t, rmsEngine)
				}
			}
		})
	}
}
