package ediecl2010

import (
	"fmt"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

const (
	K2018IDNameLength        = 40
	K2018IDNameOffset        = 2
	MonitorIDlsByteIndicator = 5
	MonitorIDmsByteIndicator = 6
)

/*
Byte Message Format
===================

Header (8 bytes)
---------------
[0] Message type identifier
[1] Device address
[2] Command code
[3] Response status
[4] Reserved byte
[5] Monitor ID ls byte indicator
[6] Monitor ID ms byte indicator
[7] Reserved byte

Monitor Name (40/42 bytes depending on firmware revision)
--------------------------------------------------------
[8] 	Reserved byte
[9-47/49] 	Monitor name

Checksum (1 byte)
----------------
[Last byte] XOR checksum of all previous bytes
*/

// MonitorIDandName parses the monitor ID and name from the byte message
func (device EDIECL2010) MonitorIDandName(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (monitor *helper.MonitorNameAndId, err error) {
	if header == nil {
		return nil, fmt.Errorf("%w header is nil", helper.ErrMsgHeaderRecordNil)
	}

	if err = helper.ValidateChecksum(byteMsg); err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	byteMessageLength := HeaderLength + K2018IDNameLength + ChecksumLength
	monitorID := 0
	monitorName := ""
	if int64(header.CommVersion) >= 0x27 && int64(header.FirmwareRevision) > 0x50 {
		byteMessageLength += K2018IDNameOffset
	}

	if !helper.VerifyByteLen(byteMsg, byteMessageLength) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), byteMessageLength)
	}

	if int64(header.FirmwareRevision) > 0x50 {
		monitorID = int(helper.ConvertLSandMStoUnint16(byteMsg[MonitorIDlsByteIndicator], byteMsg[MonitorIDmsByteIndicator]))
		monitorName = helper.GetMonitorName(byteMsg[HeaderLength+1 : byteMessageLength-ChecksumLength])
	}

	monitor = &helper.MonitorNameAndId{
		DeviceModel: DeviceModel,
		MonitorId:   int64(monitorID),
		MonitorName: monitorName,
	}

	return monitor, nil
}
