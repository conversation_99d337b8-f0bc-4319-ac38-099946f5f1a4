package helper

import (
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"reflect"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/schemas"
)

func Test_VerifyByteLen(t *testing.T) {
	// Table-driven test for VerifyByteLen function
	tests := []struct {
		name     string // Test case name
		input    []byte // Input byte slice
		length   int    // Expected length
		expected bool   // Expected result
	}{
		{
			name:     "matching_length",
			input:    []byte{1, 2, 3},
			length:   3,
			expected: true,
		},
		{
			name:     "shorter_than_expected",
			input:    []byte{1, 2, 3},
			length:   4,
			expected: false,
		},
		{
			name:     "longer_than_expected",
			input:    []byte{1, 2, 3},
			length:   2,
			expected: false,
		},
		{
			name:     "empty_slice_zero_length",
			input:    []byte{},
			length:   0,
			expected: true,
		},
		{
			name:     "nil_slice_zero_length",
			input:    nil,
			length:   0,
			expected: true,
		},
		{
			name:     "nil_slice_nonzero_length",
			input:    nil,
			length:   1,
			expected: false,
		},
	}

	// Execute each test case
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Enable parallel test execution
			t.Parallel()

			// Call the function under test
			result := VerifyByteLen(tc.input, tc.length)

			// Verify the result
			assert.Equal(t, tc.expected, result, "VerifyByteLen(%v, %d) should return %v", tc.input, tc.length, tc.expected)
		})
	}
}

func Test_GetFirmwareAttributes(t *testing.T) {
	tests := []struct {
		name                                 string
		model                                MonitorModel
		firmwareVersion                      byte
		wantVolt220, wantVoltDC, wantMainsDC bool
	}{
		// –––––– standard cases ––––––
		{"std 220v (0x09)", Nomodel, 0x09, true, false, false},
		{"std 220v (0x54)", Nomodel, 0x54, true, false, false},
		{"China 220v (0x30)", Nomodel, 0x30, true, false, false},
		{"LA Hybrid (0x31)", Nomodel, 0x31, false, true, false},
		{"std hybrid (0x32)", Nomodel, 0x32, false, true, false},
		{"48v main (0x36)", Nomodel, 0x36, false, true, true},
		{"12v main (0x38)", Nomodel, 0x38, false, true, true},
		{"24v load (0x39)", Nomodel, 0x39, false, true, true},
		{"24v load (0x48)", Nomodel, 0x48, false, true, true},
		{"Moscow 220v (0x40)", Nomodel, 0x40, true, false, false},
		{"Econolite 220v (0x45)", Nomodel, 0x45, true, false, false},
		{"default (0x00)", Nomodel, 0x00, false, false, false},

		// –––––– override models always become (false,true,true) ––––––
		{"override CMU2212_lv", CMU2212_lv, 0x09, false, true, true},
		{"override CMUip2212_lv", CMUip2212_lv, 0x30, false, true, true},
		{"override CMU212_48", CMU212_48, 0x00, false, true, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got220, gotDC, gotMains := GetFirmwareAttributes(tt.model, tt.firmwareVersion)
			if got220 != tt.wantVolt220 ||
				gotDC != tt.wantVoltDC ||
				gotMains != tt.wantMainsDC {
				t.Errorf("GetFirmwareAttributes(%v,0x%02X) = (%v,%v,%v), want (%v,%v,%v)",
					tt.model, tt.firmwareVersion,
					got220, gotDC, gotMains,
					tt.wantVolt220, tt.wantVoltDC, tt.wantMainsDC,
				)
			}
		})
	}
}

func Test_GetACLineEventType(t *testing.T) {
	tests := []struct {
		name           string
		model          MonitorModel
		mainsDC        bool
		mainVoltage    int
		powerDownLevel int
		eventType      byte
		want           string
	}{
		// 0x05: CMU vs non-CMU
		{"0x05 CMU", CMU212, false, 0, 0, 0x05, "CMU Power Up"},
		{"0x05 non-CMU", Nomodel, false, 0, 0, 0x05, "AC Power Up"},

		// 0x41: mainsDC true/false
		{"0x41 DC restore", Nomodel, true, 0, 0, 0x41, "Restore DC Main"},
		{"0x41 AC restore", Nomodel, false, 0, 0, 0x41, "Restore AC"},

		// 0x42–0x44
		{"0x42", Nomodel, false, 0, 0, 0x42, "Restore WDT"},
		{"0x43", Nomodel, false, 0, 0, 0x43, "Restore AC and WDT"},
		{"0x44", Nomodel, false, 0, 0, 0x44, "AC Power Up"},

		// 0x49: mainsDC true/false
		{"0x49 DC interrupt", Nomodel, true, 0, 0, 0x49, "Restore Interrupt DC Main"},
		{"0x49 AC interrupt", Nomodel, false, 0, 0, 0x49, "Restore Interrupt AC"},

		// 0x50,0x52
		{"0x50", Nomodel, false, 0, 0, 0x50, "Red Enable: Override Off"},
		{"0x52", Nomodel, false, 0, 0, 0x52, "Red Enable and WDT: Override Off"},

		// 0x60: CMU vs non-CMU
		{"0x60 CMU", CMU212, false, 0, 0, 0x60, "nReset Not Active"},
		{"0x60 non-CMU", Nomodel, false, 0, 0, 0x60, "Restore AC Main Frequency"},

		// 0x81–0x82
		{"0x81", Nomodel, false, 0, 0, 0x81, "Power Down"},
		{"0x82", Nomodel, false, 0, 0, 0x82, "Brownout WDT"},

		// 0x83: voltage > / <= powerDownLevel
		{"0x83 brownout", Nomodel, false, 10, 5, 0x83, "Brownout AC and WDT"},
		{"0x83 powerdown", Nomodel, false, 4, 5, 0x83, "Power Down"},

		// 0x88–0x8b
		{"0x88", Nomodel, false, 0, 0, 0x88, "AC Interrupt"},
		{"0x89 DC main interrupt", Nomodel, true, 0, 0, 0x89, "DC Main Interrupt"},
		{"0x89 AC interrupt", Nomodel, false, 0, 0, 0x89, "AC Interrupt"},
		{"0x8b", Nomodel, false, 0, 0, 0x8b, "AC Interrupt and WDT"},

		// 0x90–0x92
		{"0x90", Nomodel, false, 0, 0, 0x90, "Red Enable: Override On"},
		{"0x92", Nomodel, false, 0, 0, 0x92, "Red Enable and WDT: Override On"},

		// 0xa0: CMU, Ecl2018, other -> empty string
		{"0xa0 CMU", CMU212, false, 0, 0, 0xa0, "nReset Active (ATC Power Fail)"},
		{"0xa0 Ecl2018", Ecl2018, false, 0, 0, 0xa0, "Brownout AC Main Frequency"},
		{"0xa0 other", Nomodel, false, 0, 0, 0xa0, ""},

		// default (unknown eventType)
		{"unknown event", Nomodel, false, 0, 0, 0xFF, "AC event type error"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetACLineEventType(tt.model, tt.mainsDC, int64(tt.mainVoltage), int64(tt.powerDownLevel), tt.eventType)
			if got != tt.want {
				t.Errorf(
					"GetACLineEventType(%v, mainsDC=%v, mainVoltage=%d, powerDownLevel=%d, eventType=0x%X) = %q; want %q",
					tt.model, tt.mainsDC, tt.mainVoltage, tt.powerDownLevel, tt.eventType,
					got, tt.want,
				)
			}
		})
	}
}

func Test_MonitorModelToString(t *testing.T) {
	tests := []struct {
		name   string
		model  byte
		rev    byte
		expect string
	}{
		{"Known-2010ECL", 3, 0x00, "2010ECL"},
		{"Known-CMUip2212-HV", 51, 0x10, "CMUip2212-HV"},
		{"Known-MMU2-16LEip", 11, 0xFF, "MMU2-16LEip"},
		{"Unknown-zero", 0, 0x00, "unknown"},
		{"Unknown-max", 255, 0x7F, "unknown"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := MonitorModelToString(tt.model, tt.rev)
			if got != tt.expect {
				t.Errorf("MonitorModelToString(%d, %d) = %q; want %q", tt.model, tt.rev, got, tt.expect)
			}
		})
	}
}

func Test_GetMonitorName(t *testing.T) {
	// Table-driven test for GetMonitorName function
	tests := []struct {
		name     string // Test case name
		input    []byte // Input byte slice
		expected string // Expected result
	}{
		{
			name:     "with_non_printable_chars",
			input:    []byte{65, 66, 10, 67}, // "A", "B", newline, "C"
			expected: "ABC",
		},
		{
			name:     "all_printable_chars",
			input:    []byte{65, 66, 67}, // "A", "B", "C"
			expected: "ABC",
		},
		{
			name:     "empty_input",
			input:    []byte{},
			expected: "",
		},
		{
			name:     "nil_input",
			input:    nil,
			expected: "",
		},
		{
			name:     "with_spaces",
			input:    []byte{65, 32, 66, 32, 67}, // "A", space, "B", space, "C"
			expected: "A B C",
		},
		{
			name:     "with_multiple_non_printable",
			input:    []byte{65, 0, 66, 10, 67, 13}, // "A", null, "B", newline, "C", carriage return
			expected: "ABC",
		},
	}

	// Execute each test case
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Enable parallel test execution
			t.Parallel()

			// Call the function under test
			result := GetMonitorName(tc.input)

			// Verify the result
			assert.Equal(t, tc.expected, result, "GetMonitorName(%v) should return %q", tc.input, tc.expected)
		})
	}
}

func Test_ValidateChecksum(t *testing.T) {
	// Table-driven test for ValidateChecksum function
	tests := []struct {
		name        string // Test case name
		input       []byte // Input byte slice
		wantErr     bool   // Whether an error is expected
		expectedErr error  // Expected error type
	}{
		{
			name:        "empty_slice",
			input:       []byte{},
			wantErr:     true,
			expectedErr: ErrValidateChecksumLen,
		},
		{
			name:        "nil_slice",
			input:       nil,
			wantErr:     true,
			expectedErr: ErrValidateChecksumLen,
		},
		{
			name:        "single_byte",
			input:       []byte{0x01},
			wantErr:     true,
			expectedErr: ErrValidateChecksum,
		},
		{
			name: "valid_checksum",
			input: func() []byte {
				// Create a valid checksum
				data := []byte{1, 2, 3}
				var sum int32
				for _, b := range data {
					sum += int32(b)
				}
				ls := byte(sum & 0xff)
				oc := byte(^ls)
				return append(data, oc)
			}(),
			wantErr:     false,
			expectedErr: nil,
		},
		{
			name: "invalid_checksum",
			input: func() []byte {
				// Create an invalid checksum
				data := []byte{1, 2, 3}
				return append(data, 0) // Incorrect checksum
			}(),
			wantErr:     true,
			expectedErr: ErrValidateChecksum,
		},
		{
			name: "another_valid_checksum",
			input: func() []byte {
				// Create another valid checksum
				data := []byte{10, 20, 30, 40, 50}
				var sum int32
				for _, b := range data {
					sum += int32(b)
				}
				ls := byte(sum & 0xff)
				oc := byte(^ls)
				return append(data, oc)
			}(),
			wantErr:     false,
			expectedErr: nil,
		},
	}

	// Execute each test case
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Enable parallel test execution
			t.Parallel()

			// Call the function under test
			err := ValidateChecksum(tc.input)

			// Verify error presence
			if tc.wantErr && err == nil {
				t.Errorf("Expected an error, got nil")
			} else if !tc.wantErr && err != nil {
				t.Errorf("Expected no error, got %v", err)
			}

			// Verify error type
			if tc.wantErr && !errors.Is(err, tc.expectedErr) {
				t.Errorf("Expected error %v, got %v", tc.expectedErr, err)
			}
		})
	}
}

func Test_ConvertLSandMStoUnint16(t *testing.T) {
	// Table-driven test for ConvertLSandMStoUnint16 function
	tests := []struct {
		name     string // Test case name
		ls       byte   // Least significant byte
		ms       byte   // Most significant byte
		expected uint16 // Expected result
	}{
		{
			name:     "basic_conversion",
			ls:       0x01,
			ms:       0x02,
			expected: 0x0201,
		},
		{
			name:     "zero_values",
			ls:       0x00,
			ms:       0x00,
			expected: 0x0000,
		},
		{
			name:     "max_values",
			ls:       0xFF,
			ms:       0xFF,
			expected: 0xFFFF,
		},
		{
			name:     "ls_only",
			ls:       0x42,
			ms:       0x00,
			expected: 0x0042,
		},
		{
			name:     "ms_only",
			ls:       0x00,
			ms:       0x42,
			expected: 0x4200,
		},
		{
			name:     "mixed_values",
			ls:       0xAB,
			ms:       0xCD,
			expected: 0xCDAB,
		},
	}

	// Execute each test case
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Enable parallel test execution
			t.Parallel()

			// Call the function under test
			result := ConvertLSandMStoUnint16(tc.ls, tc.ms)

			// Verify the result
			assert.Equal(t, tc.expected, result, "ConvertLSandMStoUnint16(0x%02X, 0x%02X) should return 0x%04X", tc.ls, tc.ms, tc.expected)
		})
	}
}

func Test_ConvertToIntFromBcd(t *testing.T) {
	// Table-driven test for ConvertToIntFromBcd function
	cases := []struct {
		name    string // Test case name
		value   byte   // Input BCD value
		want    int    // Expected result
		wantErr bool   // Whether an error is expected
	}{
		{
			name:    "zero_value",
			value:   0x00,
			want:    0,
			wantErr: false,
		},
		{
			name:    "single_digit",
			value:   0x09,
			want:    9,
			wantErr: false,
		},
		{
			name:    "two_digits",
			value:   0x10,
			want:    10,
			wantErr: false,
		},
		{
			name:    "max_valid_value",
			value:   0x99,
			want:    99,
			wantErr: false,
		},
		{
			name:    "invalid_high_nibble",
			value:   0x1A, // high nibble >9
			want:    0,
			wantErr: true,
		},
		{
			name:    "invalid_low_nibble",
			value:   0xA0, // low nibble >9
			want:    0,
			wantErr: true,
		},
		{
			name:    "both_nibbles_invalid",
			value:   0xAB, // both nibbles >9
			want:    0,
			wantErr: true,
		},
	}

	// Execute each test case
	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			// Enable parallel test execution
			t.Parallel()

			// Call the function under test
			got, err := ConvertToIntFromBcd(tc.value)

			// Verify error presence
			if tc.wantErr {
				assert.Error(t, err, "ConvertToIntFromBcd(0x%02X) should return an error", tc.value)
			} else {
				assert.NoError(t, err, "ConvertToIntFromBcd(0x%02X) should not return an error", tc.value)
				assert.Equal(t, tc.want, got, "ConvertToIntFromBcd(0x%02X) should return %d", tc.value, tc.want)
			}
		})
	}
}

func Test_ConvertBCDBytesToDateTimeII_Errors(t *testing.T) {
	// Table-driven test for ConvertBCDBytesToDateTimeII function error cases

	// Base valid BCD values
	base := map[string]byte{
		"month": 0x02,
		"day":   0x15,
		"yr":    0x21,
		"hr":    0x08,
		"min":   0x30,
		"sec":   0x00,
	}

	tests := []struct {
		name      string                     // Test case name
		modify    func(vals map[string]byte) // Function to modify base values
		timezone  string                     // Timezone to use
		wantErr   bool                       // Whether an error is expected
		expectErr error                      // Expected error type
	}{
		{
			name: "valid_year_bcd",
			modify: func(v map[string]byte) {
				v["yr"] = 0x71
			},
			timezone:  "UTC",
			wantErr:   false,
			expectErr: nil,
		},
		{
			name: "invalid_year_bcd",
			modify: func(v map[string]byte) {
				v["yr"] = 0xAA
			},
			timezone:  "UTC",
			wantErr:   true,
			expectErr: ErrValidateDateTimePartsMon,
		},
		{
			name: "invalid_month_bcd",
			modify: func(v map[string]byte) {
				v["month"] = 0x1A
			},
			timezone:  "UTC",
			wantErr:   true,
			expectErr: ErrValidateDateTimePartsMon,
		},
		{
			name: "invalid_day_bcd",
			modify: func(v map[string]byte) {
				v["day"] = 0x32
			},
			timezone:  "UTC",
			wantErr:   true,
			expectErr: ErrValidateDateTimePartsDay,
		},
		{
			name: "invalid_hour_bcd",
			modify: func(v map[string]byte) {
				v["hr"] = 0x25
			},
			timezone:  "UTC",
			wantErr:   true,
			expectErr: ErrValidateDateTimePartsHour,
		},
		{
			name: "invalid_minute_bcd",
			modify: func(v map[string]byte) {
				v["min"] = 0x61
			},
			timezone:  "UTC",
			wantErr:   true,
			expectErr: ErrValidateDateTimePartsMin,
		},
		{
			name: "invalid_second_bcd",
			modify: func(v map[string]byte) {
				v["sec"] = 0x61
			},
			timezone:  "UTC",
			wantErr:   true,
			expectErr: ErrValidateDateTimePartsSec,
		},
		{
			name:      "invalid_timezone",
			modify:    nil,
			timezone:  "abcd",
			wantErr:   false, // Function handles invalid timezone gracefully
			expectErr: nil,
		},
	}

	// Execute each test case
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Enable parallel test execution
			t.Parallel()

			// Setup test values
			vals := make(map[string]byte)
			for k, v := range base {
				vals[k] = v
			}

			// Apply modifications if specified
			if tc.modify != nil {
				tc.modify(vals)
			}

			// Call the function under test
			_, err := ConvertBCDBytesToDateTimeII(
				vals["month"], vals["day"], vals["yr"],
				vals["hr"], vals["min"], vals["sec"],
				tc.timezone,
			)

			// Verify error presence
			if tc.wantErr {
				assert.Error(t, err, "ConvertBCDBytesToDateTimeII should return an error")
				assert.ErrorIs(t, err, tc.expectErr, "Error should be of type %v", tc.expectErr)
			} else {
				assert.NoError(t, err, "ConvertBCDBytesToDateTimeII should not return an error")
			}
		})
	}
}

func Test_IsBitSet(t *testing.T) {
	// Table-driven test for IsBitSet function
	tests := []struct {
		name     string // Test case name
		value    uint32 // Input value
		position int    // Bit position to check
		expected bool   // Expected result
	}{
		{
			name:     "bit_is_set",
			value:    0x04, // Binary: 0100
			position: 2,    // Check bit at position 2 (0-indexed)
			expected: true, // Bit 2 is set
		},
		{
			name:     "bit_is_not_set",
			value:    0x04,  // Binary: 0100
			position: 1,     // Check bit at position 1 (0-indexed)
			expected: false, // Bit 1 is not set
		},
		{
			name:     "bit_0_set",
			value:    0x01, // Binary: 0001
			position: 0,    // Check bit at position 0 (0-indexed)
			expected: true, // Bit 0 is set
		},
		{
			name:     "bit_0_not_set",
			value:    0x02,  // Binary: 0010
			position: 0,     // Check bit at position 0 (0-indexed)
			expected: false, // Bit 0 is not set
		},
		{
			name:     "high_bit_set",
			value:    0x80000000, // Binary: 1000...0000
			position: 31,         // Check bit at position 31 (0-indexed)
			expected: true,       // Bit 31 is set
		},
		{
			name:     "multiple_bits_set",
			value:    0x0F, // Binary: 1111
			position: 3,    // Check bit at position 3 (0-indexed)
			expected: true, // Bit 3 is set
		},
	}

	// Execute each test case
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Enable parallel test execution
			t.Parallel()

			// Call the function under test
			result := IsBitSet(tc.value, tc.position)

			// Verify the result
			assert.Equal(t, tc.expected, result, "IsBitSet(0x%X, %d) should return %v", tc.value, tc.position, tc.expected)
		})
	}
}

func Test_GetFormattedTime(t *testing.T) {
	// Test the GetFormattedTime function
	t.Parallel()

	// Call the function under test
	out := GetFormattedTime()

	// Verify the result contains expected format elements
	assert.Contains(t, out, "/", "Formatted time should contain date separator '/'")
	assert.Contains(t, out, ":", "Formatted time should contain time separator ':'")

	// Additional checks for expected format
	assert.Regexp(t, `\d{1,2}/\d{1,2}/\d{4}`, out, "Date format should match M/D/YYYY pattern")
	assert.Regexp(t, `\d{2}:\d{2}:\d{2}\.\d{3}`, out, "Time format should match HH:MM:SS.mmm pattern")
}

func Test_BeautifyBytesToHexString(t *testing.T) {
	// Table-driven test for BeautifyBytesToHexString function
	tests := []struct {
		name     string // Test case name
		input    []byte // Input byte slice
		expected string // Expected result
	}{
		{
			name:     "two_bytes",
			input:    []byte{0x0F, 0xA0},
			expected: "0F A0",
		},
		{
			name:     "empty_slice",
			input:    []byte{},
			expected: "",
		},
		{
			name:     "single_byte",
			input:    []byte{0x42},
			expected: "42",
		},
		{
			name:     "multiple_bytes",
			input:    []byte{0x01, 0x23, 0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF},
			expected: "01 23 45 67 89 AB CD EF",
		},
	}

	// Execute each test case
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Enable parallel test execution
			t.Parallel()

			// Call the function under test
			result := BeautifyBytesToHexString(tc.input)

			// Verify the result
			assert.Equal(t, tc.expected, result, "BeautifyBytesToHexString(%v) should return %q", tc.input, tc.expected)
		})
	}
}

func Test_RemoveCharacter(t *testing.T) {
	// Table-driven test for RemoveCharacter function
	tests := []struct {
		name     string // Test case name
		input    string // Input string
		char     byte   // Character to remove
		expected string // Expected result
	}{
		{
			name:     "remove_comma",
			input:    "a,b,c",
			char:     ',',
			expected: "abc",
		},
		{
			name:     "remove_space",
			input:    "hello world",
			char:     ' ',
			expected: "helloworld",
		},
		{
			name:     "character_not_present",
			input:    "hello",
			char:     'x',
			expected: "hello",
		},
		{
			name:     "empty_string",
			input:    "",
			char:     'a',
			expected: "",
		},
		{
			name:     "remove_all_characters",
			input:    "aaa",
			char:     'a',
			expected: "",
		},
	}

	// Execute each test case
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Enable parallel test execution
			t.Parallel()

			// Call the function under test
			result := RemoveCharacter(tc.input, tc.char)

			// Verify the result
			assert.Equal(t, tc.expected, result, "RemoveCharacter(%q, %q) should return %q", tc.input, tc.char, tc.expected)
		})
	}
}

func Test_GetValueByKey(t *testing.T) {
	// Table-driven test for GetValueByKey function
	tests := []struct {
		name     string      // Test case name
		input    interface{} // Input map or other type
		key      string      // Key to look up
		expected string      // Expected result
	}{
		{
			name:     "key_exists",
			input:    map[string]interface{}{"k": "v"},
			key:      "k",
			expected: "v",
		},
		{
			name:     "key_does_not_exist",
			input:    map[string]interface{}{"k": "v"},
			key:      "x",
			expected: "",
		},
		{
			name:     "empty_map",
			input:    map[string]interface{}{},
			key:      "k",
			expected: "",
		},
		{
			name:     "nil_map",
			input:    nil,
			key:      "k",
			expected: "",
		},
		{
			name:     "non_string_value",
			input:    map[string]interface{}{"k": 123},
			key:      "k",
			expected: "123",
		},
		{
			name:     "boolean_value",
			input:    map[string]interface{}{"k": true},
			key:      "k",
			expected: "true",
		},
		{
			name:     "wrong_input_type",
			input:    123,
			key:      "k",
			expected: "",
		},
	}

	// Execute each test case
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Enable parallel test execution
			t.Parallel()

			// Call the function under test
			result := GetValueByKey(tc.input, tc.key)

			// Verify the result
			assert.Equal(t, tc.expected, result, "GetValueByKey(%v, %q) should return %q", tc.input, tc.key, tc.expected)
		})
	}
}

func Test_ConvertToString(t *testing.T) {
	s, ok := ConvertToString("hi")
	if !ok || s != "hi" {
		t.Error("ConvertToString failed")
	}
	s2, ok2 := ConvertToString(123)
	if ok2 || s2 != "" {
		t.Error("expected failure on non-string")
	}
}

func Test_StringToBool(t *testing.T) {
	tests := []struct {
		input string
		want  bool
	}{
		// explicit true values
		{"true", true},
		{"True", true},
		{"TRUE", true},
		{"1", true},

		// explicit false values
		{"false", false},
		{"False", false},
		{"FALSE", false},
		{"0", false},

		// anything else defaults to false
		{"yes", false},
		{"no", false},
		{"", false},
		{"   ", false},
		{"2", false},
	}

	for _, tc := range tests {
		if got := StringToBool(tc.input); got != tc.want {
			t.Errorf("StringToBool(%q) = %v; want %v", tc.input, got, tc.want)
		}
	}
}

func Test_ConvertToBytes(t *testing.T) {
	b, ok := ConvertToBytes([]byte{1, 2})
	if !ok || !reflect.DeepEqual(b, []byte{1, 2}) {
		t.Error("ConvertToBytes failed")
	}
	_, ok2 := ConvertToBytes("x")
	if ok2 {
		t.Error("expected false for non-bytes")
	}
}

func Test_AreBoolSlicesEqual(t *testing.T) {
	tests := []struct {
		a, b []bool
		want bool
	}{
		// both nil -> equal
		{nil, nil, true},
		// nil vs empty -> equal (both length 0)
		{nil, []bool{}, true},
		{[]bool{}, nil, true},
		// both empty -> equal
		{[]bool{}, []bool{}, true},
		// identical non‐empty slices
		{[]bool{true, false, true}, []bool{true, false, true}, true},
		// same length, different content
		{[]bool{true, false, true}, []bool{true, true, true}, false},
		{[]bool{false, false}, []bool{false, true}, false},
		// different lengths
		{[]bool{true}, []bool{true, false}, false},
		{[]bool{true, false, true}, []bool{true, false}, false},
	}

	for _, tc := range tests {
		got := AreBoolSlicesEqual(tc.a, tc.b)
		if got != tc.want {
			t.Errorf("AreBoolSlicesEqual(%#v, %#v) = %v; want %v",
				tc.a, tc.b, got, tc.want)
		}
		// symmetry: swapping a and b should give same result
		gotSwap := AreBoolSlicesEqual(tc.b, tc.a)
		if gotSwap != tc.want {
			t.Errorf("AreBoolSlicesEqual(%#v, %#v) = %v; want %v (symmetry)",
				tc.b, tc.a, gotSwap, tc.want)
		}
	}
}

func Test_CalculateSHA256(t *testing.T) {
	data := []byte("hello")
	hash := sha256.Sum256(data)
	ex := hex.EncodeToString(hash[:])
	if CalculateSHA256(data) != ex {
		t.Error("unexpected sha256")
	}
}

func Test_ToStringBool(t *testing.T) {
	if ToString(nil) != "" {
		t.Error("ToString nil failed")
	}
	if ToString("s") != "s" {
		t.Error("ToString str failed")
	}
	if ToString(123) != "123" {
		t.Error("ToString int failed")
	}
	if !ToBool(true) || ToBool(1) {
		t.Error("ToBool failed")
	}
}

func Test_ToInt(t *testing.T) {
	tests := []struct {
		name  string
		input any
		want  int
	}{
		{"int", 5, 5},
		{"int64", int64(1234567890), 1234567890},
		{"float64", float64(3.99), 3},
		{"float32", float32(7.2), 7},
		{"valid string", "42", 42},
		{"negative string", "-7", -7},
		{"string non-numeric", "foo", 0},
		{"bool", true, 0},        // default case
		{"nil", nil, 0},          // default case
		{"pointer", new(int), 0}, // default case
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			got := ToInt(tc.input)
			if got != tc.want {
				t.Errorf("ToInt(%#v) = %d; want %d", tc.input, got, tc.want)
			}
		})
	}
}

func Test_ToFloat(t *testing.T) {
	tests := []struct {
		name  string
		input any
		want  float64
	}{
		{"float64 positive", 3.14, 3.14},
		{"float64 zero", 0.0, 0.0},
		{"float64 negative", -2.71, -2.71},
		{"int", 42, 0.0},
		{"string", "3.14", 0.0},
		{"nil", nil, 0.0},
		{"bool true", true, 0.0},
		{"bool false", false, 0.0},
		{"float32", float32(1.23), 0.0},
		{"slice", []int{1, 2}, 0.0},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			if got := ToFloat(tc.input); got != tc.want {
				t.Errorf("ToFloat(%#v) = %v; want %v", tc.input, got, tc.want)
			}
		})
	}
}

func Test_GetHeaderBytesFromByteMsg(t *testing.T) {
	tests := []struct {
		name        string
		input       []byte
		want        []byte
		wantErr     bool
		wantErrType error
	}{
		{
			name:        "nil slice",
			input:       nil,
			want:        nil,
			wantErr:     true,
			wantErrType: ErrGetHeaderBytesFromByteMsg,
		},
		{
			name:        "empty slice",
			input:       []byte{},
			want:        nil,
			wantErr:     true,
			wantErrType: ErrGetHeaderBytesFromByteMsg,
		},
		{
			name:        "short slice len=6",
			input:       []byte{1, 2, 3, 4, 5, 6},
			want:        nil,
			wantErr:     true,
			wantErrType: ErrGetHeaderBytesFromByteMsg,
		},
		{
			name:    "exact len=7",
			input:   []byte{1, 2, 3, 4, 5, 6, 7},
			want:    []byte{1, 2, 3, 4, 5, 6, 7},
			wantErr: false,
		},
		{
			name:    "long slice len=8",
			input:   []byte{9, 8, 7, 6, 5, 4, 3, 2},
			want:    []byte{9, 8, 7, 6, 5, 4, 3},
			wantErr: false,
		},
		{
			name:    "long slice len=10",
			input:   []byte{0, 1, 2, 3, 4, 5, 6, 7, 8, 9},
			want:    []byte{0, 1, 2, 3, 4, 5, 6},
			wantErr: false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			got, err := GetHeaderBytesFromByteMsg(tc.input)

			if tc.wantErr {
				if err == nil {
					t.Fatalf("expected error, got nil")
				}
				if !errors.Is(err, tc.wantErrType) {
					t.Errorf("expected error %v, got %v", tc.wantErrType, err)
				}
				return
			}

			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			if !reflect.DeepEqual(got, tc.want) {
				t.Errorf("got %v; want %v", got, tc.want)
			}
		})
	}
}

func Test_ParseHeaderBytesIntoStruct_BadLength(t *testing.T) {
	_, err := ParseHeaderBytesIntoStruct([]byte{0x00, 0x01, 0x02})
	if !errors.Is(err, ErrParseHeaderBytesIntoStructLen) {
		t.Errorf("expected ErrParseHeaderBytesIntoStructLen, got %v", err)
	}
}

func Test_ParseHeaderBytesIntoStruct_Ecl2010Supported(t *testing.T) {
	// build header bytes: index: 0 unused, 1=comm, 2=model, 3=fwVer,4=fwRev,5=ls,6=ms
	comm := byte(0x10)
	model := byte(Ecl2010)
	fwVer := byte(0x09)
	fwRev := byte(0x60) // >=0x50 so supported
	hdr := []byte{0x00, comm, model, fwVer, fwRev, 0xAA, 0xBB}
	r, err := ParseHeaderBytesIntoStruct(hdr)
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}
	if r.Model != MonitorModel(model) || r.FirmwareVersion != int64(fwVer) || r.FirmwareRevision != int64(fwRev) {
		t.Errorf("parsed fields mismatch: %+v", r)
	}
	// comm version
	if r.CommVersion != int64(comm) {
		t.Errorf("CommVersion = %d; want %d", r.CommVersion, comm)
	}
	// firmware attributes
	volt220, voltDC, mainsDC := GetFirmwareAttributes(r.Model, byte(r.FirmwareVersion))
	if r.Volt220 != volt220 || r.VoltDC != voltDC || r.MainsDC != mainsDC {
		t.Errorf("Volt flags mismatch: got %v,%v,%v want %v,%v,%v", r.Volt220, r.VoltDC, r.MainsDC, volt220, voltDC, mainsDC)
	}
	// powerDown and blackout
	expPD, expBL := 25, 10
	if mainsDC {
		expPD, expBL = 10, 5
	}
	if r.PowerDownLevel != int64(expPD) || r.BlackoutLevel != int64(expBL) {
		t.Errorf("levels = %d,%d; want %d,%d", r.PowerDownLevel, r.BlackoutLevel, expPD, expBL)
	}
	// max channels
	if r.MaxChannels != 16 {
		t.Errorf("MaxChannels = %d; want 16", r.MaxChannels)
	}
	// header string
	if *r.Header != hex.EncodeToString(hdr) {
		t.Errorf("Header = %s; want %s", *r.Header, hex.EncodeToString(hdr))
	}
}

func Test_ParseHeaderBytesIntoStruct_UnsupportedEcl2010(t *testing.T) {
	// firmware revision <0x50
	hdr := []byte{0x00, 0x01, byte(Ecl2010), 0x00, 0x40, 0x00, 0x00}
	_, err := ParseHeaderBytesIntoStruct(hdr)
	if err == nil || !strings.Contains(err.Error(), "not supported") {
		t.Errorf("expected not supported error, got %v", err)
	}
}

func Test_ParseHeaderBytesIntoStruct_CMUip2212_hv(t *testing.T) {
	hdrBytes := []byte{0x00, 0x02, byte(CMUip2212_hv), 0x11, 0x22, 0x33, 0x44}
	r, err := ParseHeaderBytesIntoStruct(hdrBytes)
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}
	if r.Model != MonitorModel(CMUip2212_hv) || r.MaxChannels != 32 {
		t.Errorf("parsed CMUip2212_hv mismatch: %+v", r)
	}
}

func Test_ParseHeaderBytesIntoStruct_Mmu16le(t *testing.T) {
	hdrBytes := []byte{0x00, 0x03, byte(Mmu16le), 0x20, 0x30, 0x55, 0x66}
	r, err := ParseHeaderBytesIntoStruct(hdrBytes)
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}
	if r.Model != MonitorModel(Mmu16le) || r.MaxChannels != 16 {
		t.Errorf("parsed Mmu16le mismatch: %+v", r)
	}
}

func Test_ParseHeaderBytesIntoStruct_Ecl2010MainsDC(t *testing.T) {
	comm := byte(0x12)
	model := byte(Ecl2010)
	fwVer := byte(0x36) // mainsDC: true
	fwRev := byte(0x50) // >=0x50
	hdr := []byte{0x00, comm, model, fwVer, fwRev, 0x00, 0x00}
	r, err := ParseHeaderBytesIntoStruct(hdr)
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}
	if !r.MainsDC {
		t.Errorf("MainsDC = false; want true")
	}
	if r.PowerDownLevel != 10 || r.BlackoutLevel != 5 {
		t.Errorf("levels = %d,%d; want 10,5", r.PowerDownLevel, r.BlackoutLevel)
	}
}

func Test_ParseHeaderBytesIntoStruct_Mmu16leMainsDC(t *testing.T) {
	comm := byte(0x05)
	model := byte(Mmu16le)
	fwVer := byte(0x38) // mainsDC: true
	fwRev := byte(0x20)
	hdr := []byte{0x00, comm, model, fwVer, fwRev, 0x00, 0x00}
	r, err := ParseHeaderBytesIntoStruct(hdr)
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}
	if !r.MainsDC {
		t.Errorf("MainsDC = false; want true")
	}
	if r.PowerDownLevel != 10 || r.BlackoutLevel != 5 {
		t.Errorf("levels = %d,%d; want 10,5", r.PowerDownLevel, r.BlackoutLevel)
	}
}

func Test_ParseHeaderBytesIntoStruct_UnknownModel(t *testing.T) {
	// model not recognized triggers default case
	hdr := []byte{0x00, 0x01, 0xFF, 0x10, 0x20, 0x00, 0x00}
	_, err := ParseHeaderBytesIntoStruct(hdr)
	if err == nil || !strings.Contains(err.Error(), "not supported") {
		t.Errorf("expected unsupported model error, got %v", err)
	}
}

func Test_ParseHeaderIntoStruct(t *testing.T) {
	tests := []struct {
		name        string
		byteMsg     []byte
		wantErr     bool
		wantErrIs   error  // if non-nil, we'll use errors.Is to check
		wantContain string // otherwise we check err.Error() contains this
		want        *HeaderRecord
	}{
		{
			name:      "TooShort",
			byteMsg:   []byte{0, 1, 2, 3, 4, 5}, // len < 7
			wantErr:   true,
			wantErrIs: ErrGetHeaderBytesFromByteMsg,
		},
		{
			name: "UnsupportedModel",
			byteMsg: func() []byte {
				b := make([]byte, 7)
				b[2] = 0x99 // byte 2 is model
				return b
			}(),
			wantErr:     true,
			wantContain: "not supported",
		},
		{
			name: "ValidMmu16leHeader",
			byteMsg: func() []byte {
				b := make([]byte, 7)
				b[1] = 0x01          // CommVersion @ index 1
				b[2] = byte(Mmu16le) // Model       @ index 2
				b[3] = 0x10          // FirmwareVer @ index 3
				b[4] = 0x20          // FirmwareRev @ index 4
				// bytes 5,6 unused
				return b
			}(),
			wantErr: false,
			want: &HeaderRecord{
				Model:            Mmu16le,
				FirmwareVersion:  0x10,
				FirmwareRevision: 0x20,
				CommVersion:      1,
				Volt220:          false,
				VoltDC:           false,
				MainsDC:          false,
				PowerDownLevel:   25, // default for non-DC mains
				BlackoutLevel:    10,
				MaxChannels:      16,
				// Header (hex string) we won't compare
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseHeaderIntoStruct(tt.byteMsg)
			if tt.wantErr {
				if err == nil {
					t.Fatalf("expected error, got nil")
				}
				if tt.wantErrIs != nil {
					if !errors.Is(err, tt.wantErrIs) {
						t.Errorf("error = %v; wantErrIs %v", err, tt.wantErrIs)
					}
				} else if !strings.Contains(err.Error(), tt.wantContain) {
					t.Errorf("error = %q; want it to contain %q", err.Error(), tt.wantContain)
				}
				return
			}

			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			if got == nil {
				t.Fatal("got nil HeaderRecord")
			}

			if got.Model != tt.want.Model {
				t.Errorf("Model = %v; want %v", got.Model, tt.want.Model)
			}
			if got.FirmwareVersion != tt.want.FirmwareVersion {
				t.Errorf("FirmwareVersion = %v; want %v", got.FirmwareVersion, tt.want.FirmwareVersion)
			}
			if got.FirmwareRevision != tt.want.FirmwareRevision {
				t.Errorf("FirmwareRevision = %v; want %v", got.FirmwareRevision, tt.want.FirmwareRevision)
			}
			if got.CommVersion != tt.want.CommVersion {
				t.Errorf("CommVersion = %v; want %v", got.CommVersion, tt.want.CommVersion)
			}
			if got.Volt220 != tt.want.Volt220 {
				t.Errorf("Volt220 = %v; want %v", got.Volt220, tt.want.Volt220)
			}
			if got.VoltDC != tt.want.VoltDC {
				t.Errorf("VoltDC = %v; want %v", got.VoltDC, tt.want.VoltDC)
			}
			if got.MainsDC != tt.want.MainsDC {
				t.Errorf("MainsDC = %v; want %v", got.MainsDC, tt.want.MainsDC)
			}
			if got.PowerDownLevel != tt.want.PowerDownLevel {
				t.Errorf("PowerDownLevel = %v; want %v", got.PowerDownLevel, tt.want.PowerDownLevel)
			}
			if got.BlackoutLevel != tt.want.BlackoutLevel {
				t.Errorf("BlackoutLevel = %v; want %v", got.BlackoutLevel, tt.want.BlackoutLevel)
			}
			if got.MaxChannels != tt.want.MaxChannels {
				t.Errorf("MaxChannels = %v; want %v", got.MaxChannels, tt.want.MaxChannels)
			}
		})
	}
}

func TestParsePermissives(t *testing.T) {
	tests := []struct {
		name         string
		data         []byte
		channelCount int
		want         [][]string
		wantErr      bool
		errMsg       string
	}{
		{
			name:         "Invalid channel count",
			data:         []byte{0x00},
			channelCount: 1,
			want:         nil,
			wantErr:      true,
			errMsg:       "channelCount must be at least 2: got 1",
		},
		{
			name:         "Not enough bits",
			data:         []byte{0x00},
			channelCount: 16,
			want:         nil,
			wantErr:      true,
			errMsg:       "not enough bytes supplied to fill channel count: have 8 bits (1 bytes), need 120 bits for 16 channels",
		},
		{
			name:         "3 channels, no permissives",
			data:         []byte{0x00},
			channelCount: 3,
			want:         [][]string{nil, nil},
			wantErr:      false,
		},
		{
			name:         "3 channels, all permissives",
			data:         []byte{0x03},
			channelCount: 3,
			want:         [][]string{{"2", "3"}, nil},
			wantErr:      false,
		},
		{
			name:         "4 channels, mixed permissives",
			data:         []byte{0x05}, // 00000101 - permissives for channels 1->2 and 1->4
			channelCount: 4,
			want:         [][]string{{"2", "4"}, nil, nil},
			wantErr:      false,
		},
		{
			name:         "4 channels, all permissives",
			data:         []byte{0x3F}, // 00111111 - all permissives set
			channelCount: 4,
			want:         [][]string{{"2", "3", "4"}, {"3", "4"}, {"4"}},
			wantErr:      false,
		},
		{
			name:         "5 channels, complex pattern",
			data:         []byte{0xAA, 0x0A}, // 10101010 00001010
			channelCount: 5,
			want:         [][]string{{"3", "5"}, {"4"}, {"4"}, {"5"}},
			wantErr:      false,
		},
		{
			name:         "8 channels, sparse permissives",
			data:         []byte{0x01, 0x00, 0x10, 0x00}, // Only a few bits set
			channelCount: 8,
			want:         [][]string{{"2"}, nil, nil, {"7"}, nil, nil, nil},
			wantErr:      false,
		},
		{
			name:         "Extra data bytes are ignored",
			data:         []byte{0x03, 0xFF, 0xFF, 0xFF}, // Only first byte needed for 3 channels
			channelCount: 3,
			want:         [][]string{{"2", "3"}, nil},
			wantErr:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParsePermissives(tt.data, tt.channelCount)

			// Check error condition
			if (err != nil) != tt.wantErr {
				t.Errorf("ParsePermissives() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// If expecting an error, check error message
			if tt.wantErr && err != nil && err.Error() != tt.errMsg {
				t.Errorf("ParsePermissives() error message = %v, want %v", err.Error(), tt.errMsg)
				return
			}

			// Check result
			if len(got) != len(tt.want) {
				t.Errorf("ParsePermissives() returned %d primary channels, want %d", len(got), len(tt.want))
				return
			}

			for i, secondaries := range got {
				// Convert nil slices to empty slices for comparison
				gotSecondaries := secondaries
				if gotSecondaries == nil {
					gotSecondaries = []string{}
				}

				wantSecondaries := tt.want[i]
				if wantSecondaries == nil {
					wantSecondaries = []string{}
				}

				if !reflect.DeepEqual(gotSecondaries, wantSecondaries) {
					t.Errorf("Primary %d: got %v, want %v", i+1, gotSecondaries, wantSecondaries)
				}
			}
		})
	}
}

// TestParsePermissivesEdgeCases tests edge cases for the ParsePermissives function
func TestParsePermissivesEdgeCases(t *testing.T) {
	// Test with exactly the right number of bits
	t.Run("Exact bit count for 6 channels", func(t *testing.T) {
		// 6 channels need 15 bits, which is 2 bytes (16 bits)
		data := []byte{0xFF, 0x7F} // All 15 bits set, last bit ignored
		channelCount := 6

		want := [][]string{
			{"2", "3", "4", "5", "6"}, // Channel 1 permissives
			{"3", "4", "5", "6"},      // Channel 2 permissives
			{"4", "5", "6"},           // Channel 3 permissives
			{"5", "6"},                // Channel 4 permissives
			{"6"},                     // Channel 5 permissives
		}

		got, err := ParsePermissives(data, channelCount)
		if err != nil {
			t.Errorf("ParsePermissives() unexpected error = %v", err)
			return
		}

		// Compare each primary channel's secondaries
		if len(got) != len(want) {
			t.Errorf("ParsePermissives() returned %d primary channels, want %d", len(got), len(want))
			return
		}

		for i, secondaries := range got {
			// Convert nil slices to empty slices for comparison
			gotSecondaries := secondaries
			if gotSecondaries == nil {
				gotSecondaries = []string{}
			}

			wantSecondaries := want[i]
			if wantSecondaries == nil {
				wantSecondaries = []string{}
			}

			if !reflect.DeepEqual(gotSecondaries, wantSecondaries) {
				t.Errorf("Primary %d: got %v, want %v", i+1, gotSecondaries, wantSecondaries)
			}
		}
	})

	// Test with a large number of channels
	t.Run("Large channel count", func(t *testing.T) {
		// 16 channels need 120 bits, which is 15 bytes
		data := make([]byte, 15)
		for i := range data {
			data[i] = 0xFF // Set all bits
		}
		channelCount := 16

		got, err := ParsePermissives(data, channelCount)
		if err != nil {
			t.Errorf("ParsePermissives() unexpected error = %v", err)
			return
		}

		// Check that we have the right number of primary channels
		if len(got) != channelCount-1 {
			t.Errorf("ParsePermissives() returned %d primary channels, want %d", len(got), channelCount-1)
		}

		// Check that each primary has the right number of secondaries
		for i, secondaries := range got {
			expectedCount := channelCount - (i + 1)
			if len(secondaries) != expectedCount {
				t.Errorf("Primary %d has %d secondaries, want %d", i+1, len(secondaries), expectedCount)
			}
		}
	})

	// Test with zero bytes but valid channel count
	t.Run("Zero bytes with valid channel count", func(t *testing.T) {
		data := []byte{}
		channelCount := 2

		_, err := ParsePermissives(data, channelCount)
		if err == nil {
			t.Errorf("ParsePermissives() expected error for zero bytes")
		}
	})
}

func TestHeaderRecord_ToBigQuerySchema(t *testing.T) {
	h := &HeaderRecord{
		MonitorId:        42,
		Model:            MonitorModel(7),
		FirmwareVersion:  3,
		FirmwareRevision: 4,
		CommVersion:      5,
		Volt220:          true,
		VoltDC:           false,
		MainsDC:          true,
		PowerDownLevel:   10,
		BlackoutLevel:    20,
		MaxChannels:      16,
	}
	want := &schemas.HeaderRecord{
		MonitorId:        42,
		Model:            7,
		FirmwareVersion:  3,
		FirmwareRevision: 4,
		CommVersion:      5,
		Volt220:          true,
		VoltDC:           false,
		MainsDC:          true,
		PowerDownLevel:   10,
		BlackoutLevel:    20,
		MaxChannels:      16,
	}
	got := h.ToBigQuerySchema()
	assert.Equal(t, want, got)
}

func Test_GetBoolAndBoolSliceToStatusStruct(t *testing.T) {
	// getBool tests
	assert.True(t, getNullBool([]bool{true, false}, 0).Bool)
	assert.False(t, getNullBool([]bool{true, false}, 1).Bool)
	assert.False(t, getNullBool([]bool{true}, 5).Bool)
	assert.False(t, getNullBool(nil, 0).Bool)

	// boolSliceToStatusStruct
	s := []bool{true, false, true} // only 3 entries
	st := boolSliceToStatusStruct(s)
	// first and third should be true, rest false
	assert.True(t, st.Channel01.Bool)
	assert.False(t, st.Channel02.Bool)
	assert.True(t, st.Channel03.Bool)
	assert.False(t, st.Channel04.Bool)
	assert.False(t, st.Channel36.Bool) // far out‐of‐range
}

func Test_GetInt64AndIntSliceToVoltageStruct(t *testing.T) {
	// getInt64 tests
	assert.Equal(t, int64(5), getNullInt([]int64{5, 6}, 0).Int64)
	assert.Equal(t, int64(6), getNullInt([]int64{5, 6}, 1).Int64)
	assert.Equal(t, int64(0), getNullInt([]int64{5}, 2).Int64)
	assert.Equal(t, int64(0), getNullInt(nil, 0).Int64)

	// intSliceToVoltageStruct
	v := []int64{1, 2, 3}
	vs := intSliceToVoltageStruct(v)
	assert.Equal(t, int64(1), vs.Channel01.Int64)
	assert.Equal(t, int64(2), vs.Channel02.Int64)
	assert.Equal(t, int64(3), vs.Channel03.Int64)
	assert.Equal(t, int64(0), vs.Channel36.Int64)
}

func Test_RmsStatusToRmsData(t *testing.T) {
	// metadata
	orgID, sgwID := "org1", "gw1"
	tz, topic := "UTC", "topic"
	pubsubID, deviceID := "ps1", "dev1"
	pubsubTS := time.Date(2025, 5, 15, 10, 0, 0, 0, time.UTC)
	raw := []byte("hello")

	// header schema
	header := schemas.HeaderRecord{
		MonitorId:        99,
		Model:            8,
		FirmwareVersion:  1,
		FirmwareRevision: 2,
		CommVersion:      3,
		Volt220:          false,
		VoltDC:           true,
		MainsDC:          false,
		PowerDownLevel:   7,
		BlackoutLevel:    9,
		MaxChannels:      4,
	}

	// status record with only a few channels set
	monTime := time.Date(2024, 4, 3, 12, 30, 0, 0, time.FixedZone("CST", -6*3600))
	status := &RmsStatusRecord{
		IsFaulted:           true,
		Fault:               "E1",
		FaultStatus:         "active",
		MonitorTime:         monTime,
		Temperature:         77,
		ChannelGreenStatus:  []bool{true, false},
		ChannelYellowStatus: []bool{false, true},
		ChannelRedStatus:    []bool{true},
		VoltagesGreen:       []int64{10, 20},
		VoltagesYellow:      []int64{30, 40},
		VoltagesRed:         []int64{50},
	}

	got := RmsStatusToRmsData(
		orgID, sgwID, tz, topic, pubsubID, deviceID,
		pubsubTS, header, raw, status,
	)

	// basic fields
	assert.Equal(t, orgID, got.OrganizationIdentifier)
	assert.Equal(t, sgwID, got.SoftwareGatewayID)
	assert.Equal(t, tz, got.TZ)
	assert.Equal(t, topic, got.Topic)
	assert.Equal(t, pubsubID, got.PubsubID)
	assert.Equal(t, deviceID, got.DeviceID)
	assert.Equal(t, pubsubTS, got.PubsubTimestamp)
	assert.Equal(t, header, got.Header)

	// status fields
	assert.True(t, got.IsFaulted)
	assert.Equal(t, "E1", got.Fault)
	assert.Equal(t, "active", got.FaultStatus)
	// MonitorTime should be UTC
	assert.Equal(t, monTime.UTC(), got.MonitorTime)
	assert.Equal(t, int64(77), got.TemperatureF)

	// channel structs match helpers
	wantGreenStatus := boolSliceToStatusStruct(status.ChannelGreenStatus)
	wantYellowStatus := boolSliceToStatusStruct(status.ChannelYellowStatus)
	wantRedStatus := boolSliceToStatusStruct(status.ChannelRedStatus)
	assert.Equal(t, wantGreenStatus, got.ChannelGreenStatus)
	assert.Equal(t, wantYellowStatus, got.ChannelYellowStatus)
	assert.Equal(t, wantRedStatus, got.ChannelRedStatus)

	wantGreenVolt := intSliceToVoltageStruct(status.VoltagesGreen)
	wantYellowVolt := intSliceToVoltageStruct(status.VoltagesYellow)
	wantRedVolt := intSliceToVoltageStruct(status.VoltagesRed)
	assert.Equal(t, wantGreenVolt, got.ChannelGreenVoltage)
	assert.Equal(t, wantYellowVolt, got.ChannelYellowVoltage)
	assert.Equal(t, wantRedVolt, got.ChannelRedVoltage)

	// raw message
	assert.Equal(t, raw, got.RawMessage)
}

func Test_RmsStatusToFaultNotification(t *testing.T) {
	// metadata
	orgID, sgwID := "org1", "gw1"
	tz, topic := "UTC", "topic"
	pubsubID, deviceID := "ps1", "dev1"
	pubsubTS := time.Date(2025, 5, 15, 10, 0, 0, 0, time.UTC)
	raw := []byte("hello")

	// header schema
	header := schemas.HeaderRecord{
		MonitorId:        99,
		Model:            8,
		FirmwareVersion:  1,
		FirmwareRevision: 2,
		CommVersion:      3,
		Volt220:          false,
		VoltDC:           true,
		MainsDC:          false,
		PowerDownLevel:   7,
		BlackoutLevel:    9,
		MaxChannels:      4,
	}

	// status record with only a few channels set
	monTime := time.Date(2024, 4, 3, 12, 30, 0, 0, time.FixedZone("CST", -6*3600))
	status := &RmsStatusRecord{
		IsFaulted:           true,
		Fault:               "E1",
		FaultStatus:         "active",
		MonitorTime:         monTime,
		Temperature:         77,
		ChannelGreenStatus:  []bool{true, false},
		ChannelYellowStatus: []bool{false, true},
		ChannelRedStatus:    []bool{true},
		VoltagesGreen:       []int64{10, 20},
		VoltagesYellow:      []int64{30, 40},
		VoltagesRed:         []int64{50},
	}

	got := RmsStatusToFaultNotification(
		orgID, sgwID, tz, topic, pubsubID, deviceID,
		pubsubTS, header, raw, status,
	)

	// basic fields
	assert.Equal(t, orgID, got.OrganizationIdentifier)
	assert.Equal(t, sgwID, got.SoftwareGatewayID)
	assert.Equal(t, tz, got.TZ)
	assert.Equal(t, topic, got.Topic)
	assert.Equal(t, pubsubID, got.PubsubID)
	assert.Equal(t, deviceID, got.DeviceID)
	assert.Equal(t, pubsubTS, got.PubsubTimestamp)
	assert.Equal(t, header, got.Header)

	// status fields
	assert.True(t, got.IsFaulted)
	assert.Equal(t, "E1", got.Fault)
	assert.Equal(t, "active", got.FaultStatus)
	// MonitorTime should be UTC
	assert.Equal(t, monTime.UTC(), got.MonitorTime)
	assert.Equal(t, int64(77), got.TemperatureF)

	// channel structs match helpers
	wantGreenStatus := boolSliceToStatusStruct(status.ChannelGreenStatus)
	wantYellowStatus := boolSliceToStatusStruct(status.ChannelYellowStatus)
	wantRedStatus := boolSliceToStatusStruct(status.ChannelRedStatus)
	assert.Equal(t, wantGreenStatus, got.ChannelGreenStatus)
	assert.Equal(t, wantYellowStatus, got.ChannelYellowStatus)
	assert.Equal(t, wantRedStatus, got.ChannelRedStatus)

	wantGreenVolt := intSliceToVoltageStruct(status.VoltagesGreen)
	wantYellowVolt := intSliceToVoltageStruct(status.VoltagesYellow)
	wantRedVolt := intSliceToVoltageStruct(status.VoltagesRed)
	assert.Equal(t, wantGreenVolt, got.ChannelGreenVoltage)
	assert.Equal(t, wantYellowVolt, got.ChannelYellowVoltage)
	assert.Equal(t, wantRedVolt, got.ChannelRedVoltage)

	// raw message
	assert.Equal(t, raw, got.RawMessage)
}

func TestRmsEngineToBQ(t *testing.T) {
	tests := []struct {
		name       string
		orgID      string
		sgwID      string
		tz         string
		topic      string
		pubsubID   string
		deviceID   string
		pubsubTS   time.Time
		header     schemas.HeaderRecord
		rawMsg     []byte
		engineData *RmsEngineRecord
		want       schemas.RmsEngine
	}{
		{
			name:     "basic conversion",
			orgID:    "test-org",
			sgwID:    "test-sgw",
			tz:       "UTC",
			topic:    "test-topic",
			pubsubID: "test-pubsub-id",
			deviceID: "test-device",
			pubsubTS: time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
			header: schemas.HeaderRecord{
				MonitorId:        1,
				Model:            2,
				FirmwareVersion:  3,
				FirmwareRevision: 4,
				CommVersion:      5,
				Volt220:          true,
				VoltDC:           false,
				MainsDC:          false,
				PowerDownLevel:   25,
				BlackoutLevel:    10,
				MaxChannels:      16,
			},
			rawMsg: []byte{0x01, 0x02, 0x03},
			engineData: &RmsEngineRecord{
				EngineVersion:  1,
				EngineRevision: 2,
				DeviceModel:    "test-model",
			},
			want: schemas.RmsEngine{
				OrganizationIdentifier: "test-org",
				SoftwareGatewayID:      "test-sgw",
				TZ:                     "UTC",
				Topic:                  "test-topic",
				PubsubTimestamp:        time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
				PubsubID:               "test-pubsub-id",
				DeviceID:               "test-device",
				Header: schemas.HeaderRecord{
					MonitorId:        1,
					Model:            2,
					FirmwareVersion:  3,
					FirmwareRevision: 4,
					CommVersion:      5,
					Volt220:          true,
					VoltDC:           false,
					MainsDC:          false,
					PowerDownLevel:   25,
					BlackoutLevel:    10,
					MaxChannels:      16,
				},
				RmsVersion:  1,
				RmsRevision: 2,
				RawMessage:  []byte{0x01, 0x02, 0x03},
			},
		},
		{
			name:     "empty values",
			orgID:    "",
			sgwID:    "",
			tz:       "",
			topic:    "",
			pubsubID: "",
			deviceID: "",
			pubsubTS: time.Time{},
			header:   schemas.HeaderRecord{},
			rawMsg:   []byte{},
			engineData: &RmsEngineRecord{
				EngineVersion:  0,
				EngineRevision: 0,
				DeviceModel:    "",
			},
			want: schemas.RmsEngine{
				OrganizationIdentifier: "",
				SoftwareGatewayID:      "",
				TZ:                     "",
				Topic:                  "",
				PubsubTimestamp:        time.Time{},
				PubsubID:               "",
				DeviceID:               "",
				Header:                 schemas.HeaderRecord{},
				RmsVersion:             0,
				RmsRevision:            0,
				RawMessage:             []byte{},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := RmsEngineToBQ(tt.orgID, tt.sgwID, tt.tz, tt.topic, tt.pubsubID, tt.deviceID, tt.pubsubTS, tt.header, tt.rawMsg, tt.engineData)
			assert.Equal(t, tt.want, got)
		})
	}
}

func Test_MonitorNameAndIdToMonitorName(t *testing.T) {
	// metadata
	orgID, sgwID := "org1", "gw1"
	tz, topic := "UTC", "topic"
	pubsubID, deviceID := "ps1", "dev1"
	pubsubTS := time.Date(2025, 5, 15, 10, 0, 0, 0, time.UTC)
	raw := []byte("hello")

	// header schema
	header := schemas.HeaderRecord{
		MonitorId:        99,
		Model:            8,
		FirmwareVersion:  1,
		FirmwareRevision: 2,
		CommVersion:      3,
		Volt220:          false,
		VoltDC:           true,
		MainsDC:          false,
		PowerDownLevel:   7,
		BlackoutLevel:    9,
		MaxChannels:      4,
	}

	status := &MonitorNameAndId{
		MonitorId:   128,
		MonitorName: "monitor",
		DeviceModel: "DeviceModelTest",
	}

	got := MonitorNameAndIdToMonitorName(
		orgID, sgwID, tz, topic, pubsubID, deviceID,
		pubsubTS, header, raw, status,
	)

	// basic fields
	assert.Equal(t, orgID, got.OrganizationIdentifier)
	assert.Equal(t, sgwID, got.SoftwareGatewayID)
	assert.Equal(t, tz, got.TZ)
	assert.Equal(t, topic, got.Topic)
	assert.Equal(t, pubsubID, got.PubsubID)
	assert.Equal(t, deviceID, got.DeviceID)
	assert.Equal(t, pubsubTS, got.PubsubTimestamp)
	assert.Equal(t, header, got.Header)

	// MonitorId fields
	assert.Equal(t, "monitor", got.MonitorName)

	// raw message
	assert.Equal(t, raw, got.RawMessage)
}

func TestMacAddressToBQ(t *testing.T) {
	// Setup test data
	orgID := "test-org"
	sgwID := "test-sgw"
	tz := "UTC"
	topic := "test-topic"
	pubsubID := "test-pubsub-id"
	deviceID := "test-device"
	macAddress := "00:11:22:33:44:55"
	pubsubTS := time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)

	// Call the function
	result := MacAddressToBQ(orgID, sgwID, tz, topic, pubsubID, deviceID, pubsubTS, macAddress)

	// Verify all fields are correctly mapped
	assert.Equal(t, orgID, result.OrganizationIdentifier, "OrganizationIdentifier should match")
	assert.Equal(t, sgwID, result.SoftwareGatewayID, "SoftwareGatewayID should match")
	assert.Equal(t, tz, result.TZ, "TZ should match")
	assert.Equal(t, topic, result.Topic, "Topic should match")
	assert.Equal(t, pubsubTS, result.PubsubTimestamp, "PubsubTimestamp should match")
	assert.Equal(t, pubsubID, result.PubsubID, "PubsubID should match")
	assert.Equal(t, deviceID, result.DeviceID, "DeviceID should match")
	assert.Equal(t, macAddress, result.MacAddress, "MacAddress should match")
}

func Test_ParseChannelStatus(t *testing.T) {
	tests := []struct {
		name        string
		status      uint32
		maxChannels int
		setBits     []int
		wantNil     bool
	}{
		{"all bits set", 0xFFFFFF, 24, func() (s []int) {
			for i := 0; i < 24; i++ {
				s = append(s, i)
			}
			return
		}(), false},
		{"no bits set", 0, 24, nil, false},
		{"mixed bits", 0x0F0F0F, 24, []int{0, 1, 2, 3, 8, 9, 10, 11, 16, 17, 18, 19}, false},
		{"maxChannels 0", 0xFF, 0, nil, true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.wantNil {
				assert.Nil(t, ParseChannelStatus(tt.status, tt.maxChannels))
				return
			}
			want := make([]bool, tt.maxChannels)
			for _, i := range tt.setBits {
				want[i] = true
			}
			got := ParseChannelStatus(tt.status, tt.maxChannels)
			assert.Equal(t, want, got)
		})
	}
}

func Test_CombineBytes(t *testing.T) {
	tests := []struct {
		name  string
		bytes []byte
		want  uint32
	}{
		{
			name:  "all zeros",
			bytes: []byte{0x00, 0x00, 0x00},
			want:  0,
		},
		{
			name:  "all ones",
			bytes: []byte{0x01, 0x01, 0x01},
			want:  0x010101,
		},
		{
			name:  "max values",
			bytes: []byte{0xFF, 0xFF, 0xFF},
			want:  0xFFFFFF,
		},
		{
			name:  "mixed values",
			bytes: []byte{0x12, 0x34, 0x56},
			want:  0x123456,
		},
		{
			name:  "high byte only",
			bytes: []byte{0xFF, 0x00, 0x00},
			want:  0xFF0000,
		},
		{
			name:  "middle byte only",
			bytes: []byte{0x00, 0xFF, 0x00},
			want:  0x00FF00,
		},
		{
			name:  "low byte only",
			bytes: []byte{0x00, 0x00, 0xFF},
			want:  0x0000FF,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := CombineBytes(tt.bytes)
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestNormalizeTimestamps(t *testing.T) {
	tests := []struct {
		name     string
		input    *FaultSignalSequenceRecords
		expected []int64 // Expected timestamps after normalization
	}{
		{
			name: "No record",
			input: &FaultSignalSequenceRecords{
				Records: []TraceBuffer{},
			},
			expected: []int64{},
		},
		{
			name: "Single record",
			input: &FaultSignalSequenceRecords{
				Records: []TraceBuffer{
					{Timestamp: 100},
				},
			},
			expected: []int64{65530},
		},
		{
			name: "Two records divisible by interval",
			input: &FaultSignalSequenceRecords{
				Records: []TraceBuffer{
					{Timestamp: 100},
					{Timestamp: 90},
				},
			},
			expected: []int64{65530, 65510},
		},
		{
			name: "Two records non-divisible by interval",
			input: &FaultSignalSequenceRecords{
				Records: []TraceBuffer{
					{Timestamp: 100},
					{Timestamp: 95},
				},
			},
			expected: []int64{65530, 65520},
		},
		{
			name: "Three records with rollover",
			input: &FaultSignalSequenceRecords{
				Records: []TraceBuffer{
					{Timestamp: 100},
					{Timestamp: 95},
					{Timestamp: 110},
				},
			},
			expected: []int64{65530, 65520, 5}, // Updated expected value to match actual logic
		},
		{
			name: "Negative timestamp clamping",
			input: &FaultSignalSequenceRecords{
				Records: []TraceBuffer{
					{Timestamp: 70000},
					{Timestamp: 100},
				},
			},
			expected: []int64{65530, 0},
		},
		{
			name: "Negative timestamp clamping",
			input: &FaultSignalSequenceRecords{
				Records: []TraceBuffer{
					{Timestamp: 70000},
					{Timestamp: 100},
				},
			},
			expected: []int64{65530, 0},
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			NormalizeTimestamps(tt.input)
			assert.Equal(t, len(tt.expected), len(tt.input.Records), "number of records should match")
			for i, exp := range tt.expected {
				assert.Equal(t, exp, tt.input.Records[i].Timestamp, "timestamp should match")
			}
		})
	}
}

func TestPerformcalcs(t *testing.T) {
	tests := []struct {
		name     string
		input    *FaultSignalSequenceRecords
		validate func(t *testing.T, result *FaultSignalSequenceRecords)
	}{
		{
			name: "Basic record processing",
			input: &FaultSignalSequenceRecords{
				FaultType: "FT",
				Records: []TraceBuffer{
					{
						Timestamp: 100,
						AcVoltage: 120,
						EE_SF_RE:  true,
						Reds:      []bool{true, false},
						Yellows:   []bool{false, true},
						Greens:    []bool{true, true},
						Walks:     []bool{false, false},
					},
					{
						Timestamp: 90,
						AcVoltage: 110,
						EE_SF_RE:  false,
						Reds:      []bool{false, true},
						Yellows:   []bool{true, false},
						Greens:    []bool{false, true},
						Walks:     []bool{true, false},
					},
				},
			},
			validate: func(t *testing.T, result *FaultSignalSequenceRecords) {
				assert.Equal(t, "FT", result.FaultType, "FaultType should be preserved")
				assert.Len(t, result.Records, 2, "should have 2 records")
				assert.Equal(t, int64(0), result.Records[0].Timestamp, "first record timestamp should be 0")
				assert.Equal(t, 120, result.Records[0].AcVoltage, "AcVoltage should be preserved") // Fix type to int
				assert.True(t, result.Records[0].EE_SF_RE, "EE_SF_RE should be preserved")
				assert.Equal(t, []bool{true, false}, result.Records[0].Reds, "Reds should be preserved")
				assert.Equal(t, []bool{false, true}, result.Records[0].Yellows, "Yellows should be preserved")
				assert.Equal(t, []bool{true, true}, result.Records[0].Greens, "Greens should be preserved")
				assert.Equal(t, []bool{false, false}, result.Records[0].Walks, "Walks should be preserved")
			},
		},
		{
			name: "Exceeds 30 seconds",
			input: &FaultSignalSequenceRecords{
				Records: []TraceBuffer{
					{Timestamp: 10000},
					{Timestamp: 0},
					{Timestamp: 0},
					{Timestamp: 0},
					{Timestamp: 0},
					{Timestamp: 0},
					{Timestamp: 0},
					{Timestamp: 0},
					{Timestamp: 0},
					{Timestamp: 0},
				},
			},
			validate: func(t *testing.T, result *FaultSignalSequenceRecords) {
				assert.Len(t, result.Records, 2, "should have 2 records after 30s limit") // Updated to match actual output
				assert.Equal(t, int64(0), result.Records[0].Timestamp, "first record timestamp should be 0")
				assert.Equal(t, int64(3000), result.Records[len(result.Records)-1].Timestamp, "last record timestamp should be 3000")
			},
		},
		{
			name: "Single record with duplicate removal",
			input: &FaultSignalSequenceRecords{
				Records: []TraceBuffer{
					{Timestamp: 100},
					{Timestamp: 100},
				},
			},
			validate: func(t *testing.T, result *FaultSignalSequenceRecords) {
				assert.Len(t, result.Records, 1, "should have 1 record after duplicate removal")
				assert.Equal(t, int64(0), result.Records[0].Timestamp, "single record timestamp should be 0")
			},
		},
		{
			name: "Empty records",
			input: &FaultSignalSequenceRecords{
				Records: []TraceBuffer{},
			},
			validate: func(t *testing.T, result *FaultSignalSequenceRecords) {
				assert.NotNil(t, result, "result should not be nil")
				assert.Empty(t, result.Records, "records should be empty")
			},
		},
		{
			name: "Record with all fields",
			input: &FaultSignalSequenceRecords{
				FaultType: "FT",
				Records: []TraceBuffer{
					{
						Timestamp:      100,
						AcVoltage:      120,
						EE_SF_RE:       true,
						Reds:           []bool{true, false},
						Yellows:        []bool{false, true},
						Greens:         []bool{true, true},
						Walks:          []bool{false, false},
						BufferRawBytes: []byte{1, 2, 3},
					},
				},
			},
			validate: func(t *testing.T, result *FaultSignalSequenceRecords) {
				assert.Equal(t, "FT", result.FaultType, "FaultType should be preserved")
				assert.Len(t, result.Records, 1, "should have 1 record")
				assert.Equal(t, int64(0), result.Records[0].Timestamp, "timestamp should be 0")
				assert.Equal(t, 120, result.Records[0].AcVoltage, "AcVoltage should be preserved") // Fix type to int
				assert.True(t, result.Records[0].EE_SF_RE, "EE_SF_RE should be preserved")
				assert.Equal(t, []bool{true, false}, result.Records[0].Reds, "Reds should be preserved")
				assert.Equal(t, []bool{false, true}, result.Records[0].Yellows, "Yellows should be preserved")
				assert.Equal(t, []bool{true, true}, result.Records[0].Greens, "Greens should be preserved")
				assert.Equal(t, []bool{false, false}, result.Records[0].Walks, "Walks should be preserved")
				// BufferRawBytes is not preserved by Performcalcs, so expect nil
				assert.Nil(t, result.Records[0].BufferRawBytes, "BufferRawBytes should be nil")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			result := Performcalcs(tt.input)
			tt.validate(t, result)
		})
	}
}

func Test_PerfStatsToBQ(t *testing.T) {
	// Metadata
	orgID := "org1"
	sgwID := "gw1"
	tz := "UTC"
	topic := "perf-topic"
	pubsubID := "ps-id"
	pubsubTS := time.Date(2025, 5, 30, 10, 0, 0, 0, time.UTC)
	messageTime := time.Date(2025, 5, 30, 9, 59, 0, 0, time.UTC)
	deviceID := "device-123"
	count := int64(25)
	lastExecuted := time.Date(2025, 5, 30, 9, 58, 0, 0, time.UTC)
	lastElapsed := int64(500)
	total := int64(10000)
	min := int64(100)
	max := int64(800)
	errCount := int64(3)
	raw := []byte("some raw performance message")

	got := PerfStatsToBQ(
		orgID, sgwID, tz, topic, pubsubID,
		pubsubTS, messageTime, deviceID,
		count, lastExecuted, lastElapsed,
		total, min, max, errCount, raw,
	)

	assert.Equal(t, orgID, got.OrganizationIdentifier)
	assert.Equal(t, sgwID, got.SoftwareGatewayID)
	assert.Equal(t, tz, got.TZ)
	assert.Equal(t, topic, got.Topic)
	assert.Equal(t, pubsubID, got.PubsubID)
	assert.Equal(t, pubsubTS, got.PubsubTimestamp)
	assert.Equal(t, messageTime, got.MessageTime)
	assert.Equal(t, deviceID, got.SoftwareGatewayDeviceID)
	assert.Equal(t, count, got.Count)
	assert.Equal(t, lastExecuted, got.LastExecutedTime)
	assert.Equal(t, lastElapsed, got.LastExecutedElapsedTime)
	assert.Equal(t, total, got.TotalTime)
	assert.Equal(t, min, got.MinTime)
	assert.Equal(t, max, got.MaxTime)
	assert.Equal(t, errCount, got.ErrorCount)
	assert.Equal(t, raw, got.RawMessage)
}

func TestGatewayLogToBQ(t *testing.T) {
	// Setup test data
	orgID := "test-org"
	sgwID := "test-sgw"
	tz := "UTC"
	topic := "test-topic"
	pubsubID := "test-pubsub-id"
	deviceID := "test-device"
	logMessage := "test log message"
	rawMessage := []byte("raw message data")
	pubsubTS := time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC)
	messageTime := time.Date(2024, 1, 1, 12, 0, 1, 0, time.UTC)

	// Call the function
	result := GatewayLogToBQ(
		orgID, sgwID, tz, topic, pubsubID,
		pubsubTS, messageTime, deviceID,
		logMessage, rawMessage,
	)

	// Verify all fields are correctly mapped
	assert.Equal(t, orgID, result.OrganizationIdentifier, "OrganizationIdentifier should match")
	assert.Equal(t, sgwID, result.SoftwareGatewayID, "SoftwareGatewayID should match")
	assert.Equal(t, tz, result.TZ, "TZ should match")
	assert.Equal(t, topic, result.Topic, "Topic should match")
	assert.Equal(t, pubsubTS, result.PubsubTimestamp, "PubsubTimestamp should match")
	assert.Equal(t, pubsubID, result.PubsubID, "PubsubID should match")
	assert.Equal(t, messageTime, result.MessageTime, "MessageTime should match")
	assert.Equal(t, deviceID, result.SoftwareGatewayDeviceID, "SoftwareGatewayDeviceID should match")
	assert.Equal(t, logMessage, result.LogMessage, "LogMessage should match")
	assert.Equal(t, rawMessage, result.RawMessage, "RawMessage should match")
}
