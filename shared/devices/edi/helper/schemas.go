package helper

import (
	"net"
	"time"

	deviceHelper "synapse-its.com/shared/devices/helper"
)

// MonitorModel is derived from byte 3 in the Header
// keep in mind this equates to actual array element[2] because the array is 0-based
type MonitorModel int64

// TODO: convert the ints to bytes
const (
	Nomodel       MonitorModel = -1
	C210          MonitorModel = 0
	N210          MonitorModel = 1
	Ecl210        MonitorModel = 2
	Ecl2010       MonitorModel = 3
	E2010         MonitorModel = 4
	E210          MonitorModel = 5
	Ecl2048       MonitorModel = 6
	K2018         MonitorModel = 7
	Kcl2018       MonitorModel = 8
	Ecl2018       MonitorModel = 9
	Mmu16e        MonitorModel = 10
	Mmu16le       MonitorModel = 11 // includes MMU-16LE, MMU-16LEip, MMU2-16LE, MMU2-16LEip
	Mmu48e        MonitorModel = 12
	Mmu16lex      MonitorModel = 13 // includes MMU2-16LEX, MMU2-16LEXip
	Ssm12e        MonitorModel = 20
	Ssm6e         MonitorModel = 21
	Nsm12         MonitorModel = 22
	Nsm6          MonitorModel = 23
	Nsm12e        MonitorModel = 24
	Nsm6e         MonitorModel = 25
	Nsm3e         MonitorModel = 26
	Ssm12le       MonitorModel = 30
	Ssm6le        MonitorModel = 31
	Ssm12lec      MonitorModel = 32
	Ssm6lec       MonitorModel = 33
	Ssm48le       MonitorModel = 34
	CMU212        MonitorModel = 40
	CMU212_220    MonitorModel = 41
	CMU212_48     MonitorModel = 42
	CMU212_48H    MonitorModel = 43
	CMU212_12     MonitorModel = 44
	CMU212_24     MonitorModel = 45
	CMU2212_hv    MonitorModel = 50 // ITS Cabinet v2
	CMUip2212_hv  MonitorModel = 51 // ITS Cabinet v2
	CMU2212_lv    MonitorModel = 52 // ITS Cabinet v2
	CMUip2212_lv  MonitorModel = 53 // ITS Cabinet v2
	CMU2212_vhv   MonitorModel = 54 // ITS Cabinet v2
	CMUip2212_vhv MonitorModel = 55 // ITS Cabinet v2
)

type HeaderRecord struct {
	MonitorId        int64        // the monitor id - user assigned value
	Model            MonitorModel // the monitor model
	FirmwareVersion  int64        // the firmware version loaded onto the device
	FirmwareRevision int64        // the firmware revision loaded onto the device
	CommVersion      int64        // the comm version
	Volt220          bool
	VoltDC           bool
	MainsDC          bool
	PowerDownLevel   int64
	BlackoutLevel    int64
	MaxChannels      int64   // the max number of channels supported
	Header           *string // the raw hex for the header bytes of the device
}

type RmsStatusRecord struct {
	IsFaulted           bool      // flag indicating if the device is in a faulted state
	Fault               string    // the user-friendly fault
	FaultStatus         string    // the user-friendly fault status
	ChannelGreenStatus  []bool    // the green channel status
	ChannelYellowStatus []bool    // the yellow channel status
	ChannelRedStatus    []bool    // the red channel status
	MonitorTime         time.Time // the date/time of the monitor - converted to UTC
	Temperature         int64     // temperature in fahrenheit
	VoltagesGreen       []int64   // green channel voltages
	VoltagesYellow      []int64   // yellow channel voltages
	VoltagesRed         []int64   // red channel voltages
	DeviceModel         string
}

type RmsEngineRecord struct {
	EngineVersion  int64 // the engine version
	EngineRevision int64 // the engine revision
	DeviceModel    string
}

type MonitorNameAndId struct {
	MonitorId   int64  // the user set monitor id
	MonitorName string // the user set monitor name
	DeviceModel string
}

type ReportHeaderRecord struct {
	ModelText                    string // user-friendly model
	FirmwareTypeText             string // user-friendly firmware type
	FirmwareVersionText          string // user-friendly firmware Version
	MonitorCommVersionText       string // user-friendly comm version
	RMSEngineFirmwareTypeText    string // user-friendly rms version
	RMSEngineFirmwareVersionText string // user-friendly rms engine revision
}

type LogMonitorResetRecord struct {
	DateTime  time.Time // the datetime of the monitor reset
	ResetType string    // the reset type - specific to some monitor models
}

type LogMonitorResetRecords struct {
	Records     []LogMonitorResetRecord
	DeviceModel string
	RawMessage  []byte
}

type LogACLineEventRecord struct {
	EventType       string
	DateTime        time.Time
	LineVoltageRms  int64
	LineFrequencyHz *int32 // this is a pointer, because not all devices have this attribute
}

type LogACLineEventRecords struct {
	VoltageType int64
	Records     []LogACLineEventRecord
	DeviceModel string
	RawMessage  []byte
}

type LogPreviousFailRecord struct {
	DateTime                          time.Time
	Fault                             string // the user-friendly fault text
	AcLine                            string
	T48VDCSignalBus                   string
	RedEnable                         string
	MCCoilEE                          string
	SpecialFunction1                  string
	SpecialFunction2                  string
	WDTMonitor                        string
	T24VDCInput                       string
	T12VDCInput                       string
	Temperature                       int64
	LsFlashBit                        bool
	FaultStatus                       []bool
	ChannelGreenStatus                []bool
	ChannelYellowStatus               []bool
	ChannelRedStatus                  []bool
	ChannelWalkStatus                 []bool
	ChannelGreenFieldCheckStatus      []bool
	ChannelYellowFieldCheckStatus     []bool
	ChannelRedFieldCheckStatus        []bool
	ChannelWalkFieldCheckStatus       []bool
	ChannelGreenRecurrentPulseStatus  []bool
	ChannelYellowRecurrentPulseStatus []bool
	ChannelRedRecurrentPulseStatus    []bool
	ChannelWalkRecurrentPulseStatus   []bool
	ChannelGreenRmsVoltage            []int32
	ChannelYellowRmsVoltage           []int32
	ChannelRedRmsVoltage              []int32
	ChannelWalkRmsVoltage             []int32
	NextConflictingChannels           []bool
	ChannelRedCurrentStatus           []bool
	ChannelYellowCurrentStatus        []bool
	ChannelGreenCurrentStatus         []bool
	ChannelRedRmsCurrent              []int32
	ChannelYellowRmsCurrent           []int32
	ChannelGreenRmsCurrent            []int32
}

type LogPreviousFailRecords struct {
	Records     []LogPreviousFailRecord
	DeviceModel string
	RawMessage  []byte
}

// used by the fault signal sequence
type TraceBuffer struct {
	BufferRawBytes []byte
	Timestamp      int64 // Timestamp of the buffer, represents 50ms; with a rollover at 65530
	Reds           []bool
	Yellows        []bool
	Greens         []bool
	Walks          []bool
	EE_SF_RE       bool
	AcVoltage      int
}

type FaultSignalSequenceRecords struct {
	RawMessage  []byte        // rawMessage contains all bytes for the trace record
	FaultType   string        // faultType contains the fault type
	Records     []TraceBuffer // traceBuffers contains the detail trace information
	DeviceModel string
}

type FaultSignalSequence struct {
	RawMessage  []byte                       // rawMessage contains all bytes for the trace record
	Records     []FaultSignalSequenceRecords // traceBuffers contains the detail trace information
	DeviceModel string
}

type ConfigurationChangeLogRecord struct {
	DateTime                        time.Time
	Ch01Permissives                 []string
	Ch02Permissives                 []string
	Ch03Permissives                 []string
	Ch04Permissives                 []string
	Ch05Permissives                 []string
	Ch06Permissives                 []string
	Ch07Permissives                 []string
	Ch08Permissives                 []string
	Ch09Permissives                 []string
	Ch10Permissives                 []string
	Ch11Permissives                 []string
	Ch12Permissives                 []string
	Ch13Permissives                 []string
	Ch14Permissives                 []string
	Ch15Permissives                 []string
	Ch16Permissives                 []string
	Ch17Permissives                 []string
	Ch18Permissives                 []string
	Ch19Permissives                 []string
	Ch20Permissives                 []string
	Ch21Permissives                 []string
	Ch22Permissives                 []string
	Ch23Permissives                 []string
	Ch24Permissives                 []string
	Ch25Permissives                 []string
	Ch26Permissives                 []string
	Ch27Permissives                 []string
	Ch28Permissives                 []string
	Ch29Permissives                 []string
	Ch30Permissives                 []string
	Ch31Permissives                 []string
	RedFailEnable                   []bool
	GreenYellowDualEnable           []bool
	YellowRedDualEnable             []bool
	GreenRedDualEnable              []bool
	MinimumYellowClearanceEnable    []bool
	MinimumYellowRedClearanceEnable []bool
	FieldCheckEnableGreen           []bool
	FieldCheckEnableYellow          []bool
	FieldCheckEnableRed             []bool
	YellowEnable                    []bool
	WalkEnableTs1                   bool
	RedFaultTiming                  string
	RecurrentPulse                  bool
	WatchdogTiming                  string
	WatchdogEnableSwitch            bool
	ProgramCardMemory               bool
	GYEnable                        bool
	MinimumFlashTime                string
	CvmLatchEnable                  bool
	LogCvmFaults                    bool
	X24VIiInputThreshold            string
	X24VLatchEnable                 bool
	X24VoltInhibit                  bool
	Port_1Disable                   bool
	TypeMode                        string
	LEDguardThresholds              bool
	ForceType_16Mode                bool
	Type_12WithSdlcMode             bool
	VmCvm_24V_3XdayLatch            bool
	RedFailEnabledbySSM             bool
	DualIndicationFaultTiming       string
	WDTErrorClearonPU               bool
	MinimumFlash                    bool
	ConfigChangeFault               bool
	RedCableFault                   bool
	AcLineBrownout                  string
	PinEEPolarity                   string
	FlashingYellowArrows            []string
	FyaRedAndYellowEnable           string
	FyaRedAndGreenDisable           string
	FyaYellowTrapDetection          bool
	FYAFlashRateFault               bool
	FyaFlashRateDetection           bool
	Pplt5Suppression                string
	CheckValue                      string
	ChangeSource                    string
	// Virtual channel settings
	RedVirtualChannel    []VirtualSetting
	YellowVirtualChannel []VirtualSetting
	GreenVirtualChannel  []VirtualSetting
	// Current sense data
	CurrentSenseRedEnabled      []bool
	CurrentSenseYellowEnabled   []bool
	CurrentSenseGreenEnabled    []bool
	CurrentSenseRedThreshold    []int
	CurrentSenseYellowThreshold []int
	CurrentSenseGreenThreshold  []int
	// Dark Channel Maps data
	DarkChannelX01 []bool
	DarkChannelX02 []bool
	DarkChannelX03 []bool
	DarkChannelX04 []bool
}

// VirtualSetting represents a virtual channel setting for a specific color.
type VirtualSetting struct {
	Color         string // representing colorName
	Enabled       bool   // representing param != 0
	SourceChannel int    // representing channelNum
	SourceColor   string // representing colorSource
}

type ConfigurationChangeLogRecords struct {
	Record      []ConfigurationChangeLogRecord
	DeviceModel string
	RawMessage  []byte
}

var EtlTranslators = map[string]deviceHelper.BoolTranslator{
	"MinimumYellowClearanceEnable":      {TrueVal: ".", FalseVal: "X"},
	"MinimumYellowRedClearanceEnable":   {TrueVal: ".", FalseVal: "X"},
	"FieldCheckEnableRed":               {TrueVal: "X", FalseVal: "."},
	"FieldCheckEnableYellow":            {TrueVal: "X", FalseVal: "."},
	"FieldCheckEnableGreen":             {TrueVal: "X", FalseVal: "."},
	"GreenRedDualEnable":                {TrueVal: "X", FalseVal: "."},
	"YellowRedDualEnable":               {TrueVal: "X", FalseVal: "."},
	"GreenYellowDualEnable":             {TrueVal: "X", FalseVal: "."},
	"RedFailEnable":                     {TrueVal: "X", FalseVal: "."},
	"RecurrentPulse":                    {TrueVal: "ENABLED", FalseVal: "DISABLED"},
	"WatchdogEnableSwitch":              {TrueVal: "ENABLED", FalseVal: "DISABLED"},
	"WalkEnableTs1":                     {TrueVal: "ENABLED", FalseVal: "DISABLED"},
	"LogCvmFaults":                      {TrueVal: "ENABLED", FalseVal: "DISABLED"},
	"ProgramCardMemory":                 {TrueVal: "ENABLED", FalseVal: "DISABLED"},
	"LEDguardThresholds":                {TrueVal: "ENABLED", FalseVal: "DISABLED"},
	"ForceType_16Mode":                  {TrueVal: "ENABLED", FalseVal: "DISABLED"},
	"Type_12WithSdlcMode":               {TrueVal: "ENABLED", FalseVal: "DISABLED"},
	"VmCvm_24V_3XdayLatch":              {TrueVal: "ENABLED", FalseVal: "DISABLED"},
	"ChannelRedFieldCheckStatus":        {TrueVal: "*", FalseVal: "."},
	"ChannelYellowFieldCheckStatus":     {TrueVal: "*", FalseVal: "."},
	"ChannelGreenFieldCheckStatus":      {TrueVal: "*", FalseVal: "."},
	"ChannelWalkFieldCheckStatus":       {TrueVal: "*", FalseVal: "."},
	"ChannelRedRecurrentPulseStatus":    {TrueVal: "*", FalseVal: "."},
	"ChannelYellowRecurrentPulseStatus": {TrueVal: "*", FalseVal: "."},
	"ChannelGreenRecurrentPulseStatus":  {TrueVal: "*", FalseVal: "."},
	"ChannelWalkRecurrentPulseStatus":   {TrueVal: "*", FalseVal: "."},
	"FaultStatus":                       {TrueVal: "*", FalseVal: " "},
	"ChannelRedStatus":                  {TrueVal: "R", FalseVal: "."},
	"ChannelYellowStatus":               {TrueVal: "Y", FalseVal: "."},
	"ChannelGreenStatus":                {TrueVal: "G", FalseVal: "."},
	"ChannelWalkStatus":                 {TrueVal: "W", FalseVal: "."},
	"NextConflictingChannels":           {TrueVal: "*", FalseVal: " "},
	"LsFlashBit":                        {TrueVal: "Set", FalseVal: "Clear"},
	"CvmLatchEnable":                    {TrueVal: "LATCH", FalseVal: "NON-LATCH"},
	"X24VLatchEnable":                   {TrueVal: "LATCH", FalseVal: "NON-LATCH"},
	"X24VoltInhibit":                    {TrueVal: "Active", FalseVal: "Off"},
	"Port_1Disable":                     {TrueVal: "Active", FalseVal: "Off"},
	"FyaYellowTrapDetection":            {TrueVal: "ENABLED", FalseVal: "DISABLED"},
	"FyaFlashRateDetection":             {TrueVal: "ENABLED", FalseVal: "DISABLED"},
	"WDTErrorClearOnPU":                 {TrueVal: "ENABLED", FalseVal: "DISABLED"},
	"MinimumFlash":                      {TrueVal: "ENABLED", FalseVal: "DISABLED"},
	"ConfigChangeFault":                 {TrueVal: "ENABLED", FalseVal: "DISABLED"},
	"RedCableFault":                     {TrueVal: "ENABLED", FalseVal: "DISABLED"},
	"FYAFlashRateFault":                 {TrueVal: "ENABLED", FalseVal: "DISABLED"},
	"GYEnable":                          {TrueVal: "ENABLED", FalseVal: "DISABLED"},
	"RedFailEnabledBySSM":               {TrueVal: "ENABLED", FalseVal: "DISABLED"},
	"CurrentSenseRedEnabled":            {TrueVal: "X", FalseVal: "."},
	"CurrentSenseYellowEnabled":         {TrueVal: "X", FalseVal: "."},
	"CurrentSenseGreenEnabled":          {TrueVal: "X", FalseVal: "."},
	"ChannelRedCurrentStatus":           {TrueVal: "R", FalseVal: "."},
	"ChannelYellowCurrentStatus":        {TrueVal: "Y", FalseVal: "."},
	"ChannelGreenCurrentStatus":         {TrueVal: "G", FalseVal: "."},
	"YellowEnable":                      {TrueVal: ".", FalseVal: "X"},
}

// MetadataMessaging is sed to store device metadata
type MetadataMessaging struct {
	DeviceID                 string
	Latitude                 string
	Longitude                string
	Manufacturer             string
	Model                    string
	UserAssignedDeviceID     string
	UserAssignedDeviceName   string
	ApplicationVersion       string
	FirmwareType             string
	FirmwareVersion          string
	CommVersion              string
	RmsEngineFirmwareType    string
	RmsEngineFirmwareVersion string
	MacAddress               string
	Header                   *string // the header bytes for the edi device - helpful in identifying the model, firmware version, and firmware revision
}

// FaultDataMessaging is used to store edi device fault information
type FaultDataMessaging struct {
	DeviceID            string
	FaultReason         string
	RedChannelStatus    []bool
	YellowChannelStatus []bool
	GreenChannelStatus  []bool
}

// Stats is used to store performance stats for the various aspects of the gateway-app engine
type Stats struct {
	Count                     int64     `json:"count"`                         // the number of times the state executed
	LastExceutedTimeUTC       time.Time `json:"last_executed_time_utc"`        // the last time the state executed (in UTC)
	LastExecutedElapsedTimeMS int64     `json:"last_executed_elapsed_time_ms"` // elapse time for the state's last execution (in milliseconds)
	TotalTimeMS               int64     `json:"total_time_ms"`                 // the total time spent in the state (in milliseconds)
	MinTimeMS                 int64     `json:"min_time_ms"`                   // the minimum time spent in the state (in milliseconds)
	MaxTimeMS                 int64     `json:"max_time_ms"`                   // the maximum time spent in the state (in milliseconds)
	ErrorCount                int64     `json:"error_count"`                   // the number of errors encountered in this state
}

// InstructionEccom represents the data exchange between an Eccom client and the field device.
type InstructionEccom struct {
	Conn           net.Conn // the Eccom client connection
	ECcomRXTimeout int64    // the Eccom client Rx timeout
	Request        []byte   // the Eccom client request
	Response       []byte   // the Device response
}

// GatewayConfigChangeType is used to indicate what type of update is being pushed from the cloud to the gateway-app
type GatewayConfigChangeType uint

const (
	// CONFIGURATIONCHANGE - this is the change being pushed to the gateway-app is a configuration change
	CONFIGURATIONCHANGE GatewayConfigChangeType = iota
	// SOFTWAREUPDATE - this is if the change is a software update
	SOFTWAREUPDATE
)
