package devices

import (
	// ecl2010 "synapse-its.com/shared/devices/edi/ediecl2010"
	cmu2212 "synapse-its.com/shared/devices/edi/edicmu2212"

	mmu216le "synapse-its.com/shared/devices/edi/edimmu16le"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

var parseHeaderIntoStruct = edihelper.ParseHeaderIntoStruct

// Device defines the standard interface for edi devices
// All of these functions translate the raw bytes from their message type to a struct
type Device interface {
	LogMonitorReset(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *edihelper.HeaderRecord) (allRecords *edihelper.LogMonitorResetRecords, err error)
	LogPreviousFail(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *edihelper.HeaderRecord) (allRecords *edihelper.LogPreviousFailRecords, err error)
	LogACLineEvent(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *edihelper.HeaderRecord) (allRecords *edihelper.LogACLineEventRecords, err error)
	LogFaultSignalSequence(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *edihelper.HeaderRecord) (allRecords *edihelper.FaultSignalSequenceRecords, err error)
	LogConfiguration(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *edihelper.HeaderRecord) (allRecords *edihelper.ConfigurationChangeLogRecords, err error)
	MonitorIDandName(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *edihelper.HeaderRecord) (monitor *edihelper.MonitorNameAndId, err error)
	RMSEngineData(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *edihelper.HeaderRecord) (rmsEngineDetail *edihelper.RmsEngineRecord, err error)
	RMSStatus(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *edihelper.HeaderRecord) (rmsStatusRecord *edihelper.RmsStatusRecord, err error)
}

// GetDeviceModel returns the correct device type based on the header record passed in to it (note the header record is retrieved from the edi device)
func GetDeviceModel(headerDetail edihelper.HeaderRecord) (Device, error) {
	switch headerDetail.Model {
	case edihelper.Ecl2010:
		// TODO: Uncomment this return and remove the return nil when the functions for the device EDIECL2010 are converted.
		// return &ecl2010.EDIECL2010{}
		return nil, ErrNotImplemented
	case edihelper.CMUip2212_hv:
		return &cmu2212.EDICMU2212{}, nil
	case edihelper.Mmu16le:
		return &mmu216le.EDIMMU216LE{}, nil
	default:
		return nil, ErrNotImplemented // device not supported
	}
}

// The Process functions below are there for the used to translate the raw bytes (which will be called from pubsub and redis)
func ProcessLogMonitorReset(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte) (LogMonitorReset *edihelper.LogMonitorResetRecords, headerDetails *edihelper.HeaderRecord, err error) {
	// Declares and inits variables
	LogMonitorReset = new(edihelper.LogMonitorResetRecords)
	headerDetails = new(edihelper.HeaderRecord)

	headerDetails, err = parseHeaderIntoStruct(byteMsg)
	if err != nil {
		return nil, nil, err
	}
	// Get the device implementation:
	dev, err := GetDeviceModel(*headerDetails)
	if err != nil {
		return nil, nil, err
	}

	// Call its LogMonitorReset()
	LogMonitorReset, err = dev.LogMonitorReset(httpHeader, byteMsg, headerDetails)
	if err != nil {
		return nil, nil, err
	}
	return LogMonitorReset, headerDetails, nil
}

func ProcessLogPreviousFail(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte) (LogPreviousFail *edihelper.LogPreviousFailRecords, headerDetails *edihelper.HeaderRecord, err error) {
	// Declares and inits variables
	LogPreviousFail = new(edihelper.LogPreviousFailRecords)
	headerDetails = new(edihelper.HeaderRecord)

	headerDetails, err = parseHeaderIntoStruct(byteMsg)
	if err != nil {
		return nil, nil, err
	}
	// Get the device implementation:
	dev, err := GetDeviceModel(*headerDetails)
	if err != nil {
		return nil, nil, err
	}

	// Call its LogPreviousFail()
	LogPreviousFail, err = dev.LogPreviousFail(httpHeader, byteMsg, headerDetails)
	if err != nil {
		return nil, nil, err
	}
	return LogPreviousFail, headerDetails, nil
}

func ProcessLogACLineEvent(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte) (LogACLineEvent *edihelper.LogACLineEventRecords, headerDetails *edihelper.HeaderRecord, err error) {
	// Declares and inits variables
	LogACLineEvent = new(edihelper.LogACLineEventRecords)
	headerDetails = new(edihelper.HeaderRecord)

	headerDetails, err = parseHeaderIntoStruct(byteMsg)
	if err != nil {
		return nil, nil, err
	}
	// Get the device implementation:
	dev, err := GetDeviceModel(*headerDetails)
	if err != nil {
		return nil, nil, err
	}

	// Call its LogACLineEvent()
	LogACLineEvent, err = dev.LogACLineEvent(httpHeader, byteMsg, headerDetails)
	if err != nil {
		return nil, nil, err
	}
	return LogACLineEvent, headerDetails, nil
}

func ProcessLogFaultSignalSequence(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte) (LogFaultSignalSequence *edihelper.FaultSignalSequenceRecords, headerDetails *edihelper.HeaderRecord, err error) {
	// Declares and inits variables
	LogFaultSignalSequence = new(edihelper.FaultSignalSequenceRecords)
	headerDetails = new(edihelper.HeaderRecord)

	headerDetails, err = parseHeaderIntoStruct(byteMsg)
	if err != nil {
		return nil, nil, err
	}
	// Get the device implementation:
	dev, err := GetDeviceModel(*headerDetails)
	if err != nil {
		return nil, nil, err
	}

	// Call its LogFaultSignalSequence()
	LogFaultSignalSequence, err = dev.LogFaultSignalSequence(httpHeader, byteMsg, headerDetails)
	if err != nil {
		return nil, nil, err
	}
	return LogFaultSignalSequence, headerDetails, nil
}

func ProcessLogConfiguration(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte) (LogConfiguration *edihelper.ConfigurationChangeLogRecords, headerDetails *edihelper.HeaderRecord, err error) {
	// Declares and inits variables
	LogConfiguration = new(edihelper.ConfigurationChangeLogRecords)
	headerDetails = new(edihelper.HeaderRecord)

	headerDetails, err = parseHeaderIntoStruct(byteMsg)
	if err != nil {
		return nil, nil, err
	}
	// Get the device implementation:
	dev, err := GetDeviceModel(*headerDetails)
	if err != nil {
		return nil, nil, err
	}

	// Call its LogConfiguration()
	LogConfiguration, err = dev.LogConfiguration(httpHeader, byteMsg, headerDetails)
	if err != nil {
		return nil, nil, err
	}
	return LogConfiguration, headerDetails, nil
}

var ProcessMonitorIDandName = func(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte) (monitorDetail *edihelper.MonitorNameAndId, headerDetails *edihelper.HeaderRecord, err error) {
	// Declares and inits variables
	monitorDetail = new(edihelper.MonitorNameAndId)
	headerDetails = new(edihelper.HeaderRecord)

	headerDetails, err = parseHeaderIntoStruct(byteMsg)
	if err != nil {
		return nil, nil, err
	}
	// Get the device implementation:
	dev, err := GetDeviceModel(*headerDetails)
	if err != nil {
		return nil, nil, err
	}

	// Call its MonitorIDandName()
	monitorDetail, err = dev.MonitorIDandName(httpHeader, byteMsg, headerDetails)
	if err != nil {
		return nil, nil, err
	}
	return monitorDetail, headerDetails, nil
}

func ProcessRmsEngineData(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte) (rmsEngineDetail *edihelper.RmsEngineRecord, headerDetails *edihelper.HeaderRecord, err error) {
	// Declares and inits variables
	rmsEngineDetail = new(edihelper.RmsEngineRecord)
	headerDetails = new(edihelper.HeaderRecord)

	headerDetails, err = parseHeaderIntoStruct(byteMsg)
	if err != nil {
		return nil, nil, err
	}
	// Get the device implementation:
	dev, err := GetDeviceModel(*headerDetails)
	if err != nil {
		return nil, nil, err
	}

	// Call its RMSEngineData()
	rmsEngineDetail, err = dev.RMSEngineData(httpHeader, byteMsg, headerDetails)
	if err != nil {
		return nil, nil, err
	}
	return rmsEngineDetail, headerDetails, nil
}

var ProcessRmsData = func(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte) (rmsStatusRecord *edihelper.RmsStatusRecord, headerDetails *edihelper.HeaderRecord, err error) {
	// Declares and inits variables
	rmsStatusRecord = new(edihelper.RmsStatusRecord)
	headerDetails = new(edihelper.HeaderRecord)

	headerDetails, err = parseHeaderIntoStruct(byteMsg)
	if err != nil {
		return nil, nil, err
	}
	// Get the device implementation:
	dev, err := GetDeviceModel(*headerDetails)
	if err != nil {
		return nil, nil, err
	}

	// Call its RMSStatus()
	rmsStatusRecord, err = dev.RMSStatus(httpHeader, byteMsg, headerDetails)
	if err != nil {
		return nil, nil, err
	}
	return rmsStatusRecord, headerDetails, nil
}
