package organization

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/mocks/dbexecutor"
)

// Test helper to create a mock context with connections
func createMockContext() context.Context {
	ctx := context.Background()
	// For now, we'll create a simple context without connections
	// This will be used to test error cases where connections are missing
	return ctx
}

// Test helper to create a mock request with JSON body
func createMockRequest(method, url string, body interface{}) *http.Request {
	var reqBody *bytes.Buffer
	if body != nil {
		jsonBody, _ := json.Marshal(body)
		reqBody = bytes.NewBuffer(jsonBody)
	} else {
		reqBody = bytes.NewBuffer([]byte{})
	}

	req := httptest.NewRequest(method, url, reqBody)
	req.Header.Set("Content-Type", "application/json")
	return req
}

// Test helper to create a mock request with mux vars
func createMockRequestWithVars(method, url string, body interface{}, vars map[string]string) *http.Request {
	req := createMockRequest(method, url, body)
	req = mux.SetURLVars(req, vars)
	return req
}

// Test validateIdentifier function
func Test_validateIdentifier(t *testing.T) {
	tests := []struct {
		name        string
		identifier  string
		expectedErr error
		wantErr     bool
	}{
		{
			name:        "valid identifier",
			identifier:  "valid-org-123",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "empty identifier",
			identifier:  "",
			expectedErr: ErrInvalidIdentifier,
			wantErr:     true,
		},
		{
			name:        "whitespace only identifier",
			identifier:  "   ",
			expectedErr: ErrInvalidIdentifier,
			wantErr:     true,
		},
		{
			name:        "identifier with spaces",
			identifier:  "  valid-org-123  ",
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Execute the function under test
			err := validateIdentifier(tt.identifier)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test parseCreatAndUpdateRequest function
func Test_parseCreatAndUpdateRequest(t *testing.T) {
	tests := []struct {
		name        string
		requestBody interface{}
		expectedErr error
		wantErr     bool
		description string
	}{
		{
			name: "valid request",
			requestBody: CreateAnhUpdateOrganizationRequest{
				Description: "Valid organization description",
			},
			expectedErr: nil,
			wantErr:     false,
			description: "Valid organization description",
		},
		{
			name: "empty description",
			requestBody: CreateAnhUpdateOrganizationRequest{
				Description: "",
			},
			expectedErr: ErrInvalidDescription,
			wantErr:     true,
			description: "",
		},
		{
			name: "whitespace only description",
			requestBody: CreateAnhUpdateOrganizationRequest{
				Description: "   ",
			},
			expectedErr: ErrInvalidDescription,
			wantErr:     true,
			description: "   ",
		},
		{
			name: "description with leading/trailing spaces",
			requestBody: CreateAnhUpdateOrganizationRequest{
				Description: "  Valid description  ",
			},
			expectedErr: nil,
			wantErr:     false,
			description: "  Valid description  ",
		},
		{
			name:        "invalid JSON",
			requestBody: "invalid json",
			expectedErr: ErrInvalidRequestBody,
			wantErr:     true,
			description: "",
		},
		{
			name: "unexpected fields",
			requestBody: map[string]interface{}{
				"description": "Valid description",
				"unexpected":  "field",
			},
			expectedErr: ErrUnexpectedFields,
			wantErr:     true,
			description: "Valid description",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock request with test body
			req := createMockRequest("POST", "/organizations", tt.requestBody)

			// Execute the function under test
			result, err := parseCreatAndUpdateRequest(req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.description, result.Description)
			}
		})
	}
}

// Test Organization.ToResponse method
func Test_Organization_ToResponse(t *testing.T) {
	// Create test organization
	now := time.Now().UTC()
	org := Organization{
		Id:                     1,
		OrganizationIdentifier: "test-org-123",
		APIKey:                 "api-key-456",
		Description:            "Test Organization",
		CreatedAt:              now,
		UpdatedAt:              now.Add(time.Hour),
		DeletedAt:              nil,
		IsDeleted:              false,
	}

	// Execute the method under test
	response := org.ToResponse()

	// Assert all fields are correctly mapped
	assert.Equal(t, org.OrganizationIdentifier, response.OrganizationIdentifier)
	assert.Equal(t, org.APIKey, response.APIKey)
	assert.Equal(t, org.Description, response.Description)
	assert.Equal(t, org.CreatedAt, response.CreatedAt)
	assert.Equal(t, org.UpdatedAt, response.UpdatedAt)

	// Verify that internal fields (Id, DeletedAt, IsDeleted) are not exposed
	// This is implicit since they're not in the response struct
}

// Test createOrganization function
func Test_createOrganization(t *testing.T) {
	tests := []struct {
		name        string
		request     *CreateAnhUpdateOrganizationRequest
		setupMock   func(*dbexecutor.FakeDBExecutor)
		expectedErr error
		wantErr     bool
	}{
		{
			name: "successful creation",
			request: &CreateAnhUpdateOrganizationRequest{
				Description: "Test Organization",
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryRowStruct call
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful organization creation
					org := dest.(*Organization)
					org.Id = 1
					org.OrganizationIdentifier = "test-org-123"
					org.Description = "Test Organization"
					org.APIKey = "api-key-456"
					org.CreatedAt = time.Now().UTC()
					org.UpdatedAt = time.Now().UTC()
					org.IsDeleted = false
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name: "database error",
			request: &CreateAnhUpdateOrganizationRequest{
				Description: "Test Organization",
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			result, err := createOrganization(mockDB, tt.request)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.request.Description, result.Description)
				assert.NotEmpty(t, result.OrganizationIdentifier)
				assert.NotEmpty(t, result.APIKey)
			}
		})
	}
}

// Test getAllOrganizations function
func Test_getAllOrganizations(t *testing.T) {
	tests := []struct {
		name        string
		setupMock   func(*dbexecutor.FakeDBExecutor)
		expectedErr error
		wantErr     bool
		expectedLen int
	}{
		{
			name: "successful retrieval with organizations",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryGenericSlice call
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful organizations retrieval
					orgs := dest.(*[]Organization)
					*orgs = []Organization{
						{
							Id:                     1,
							OrganizationIdentifier: "org-1",
							Description:            "Organization 1",
							APIKey:                 "key-1",
							CreatedAt:              time.Now().UTC(),
							UpdatedAt:              time.Now().UTC(),
							IsDeleted:              false,
						},
						{
							Id:                     2,
							OrganizationIdentifier: "org-2",
							Description:            "Organization 2",
							APIKey:                 "key-2",
							CreatedAt:              time.Now().UTC(),
							UpdatedAt:              time.Now().UTC(),
							IsDeleted:              false,
						},
					}
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
			expectedLen: 2,
		},
		{
			name: "successful retrieval with no organizations",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryGenericSlice call with empty result
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate empty organizations list
					orgs := dest.(*[]Organization)
					*orgs = []Organization{}
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
			expectedLen: 0,
		},
		{
			name: "database error",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
			expectedLen: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			result, err := getAllOrganizations(mockDB)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, *result, tt.expectedLen)
			}
		})
	}
}
